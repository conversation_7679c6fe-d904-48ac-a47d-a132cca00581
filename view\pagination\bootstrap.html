{if condition="$total > 0"}
<nav aria-label="分页导航">
    <ul class="pagination pagination-sm mb-0">
        {if condition="$currentPage > 1"}
        <li class="page-item">
            <a class="page-link" href="{$url}&page=1" aria-label="首页">
                <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="{$url}&page={$currentPage-1}" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
        {else}
        <li class="page-item disabled">
            <span class="page-link">&laquo;&laquo;</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">&laquo;</span>
        </li>
        {/if}

        {if condition="$currentPage > 4"}
        <li class="page-item">
            <a class="page-link" href="{$url}&page=1">1</a>
        </li>
        {if condition="$currentPage > 5"}
        <li class="page-item disabled">
            <span class="page-link">...</span>
        </li>
        {/if}
        {/if}

        {for start="max(1, $currentPage-2)" end="min($lastPage, $currentPage+2)"}
        {if condition="$i == $currentPage"}
        <li class="page-item active">
            <span class="page-link">{$i}</span>
        </li>
        {else}
        <li class="page-item">
            <a class="page-link" href="{$url}&page={$i}">{$i}</a>
        </li>
        {/if}
        {/for}

        {if condition="$currentPage < $lastPage - 3"}
        {if condition="$currentPage < $lastPage - 4"}
        <li class="page-item disabled">
            <span class="page-link">...</span>
        </li>
        {/if}
        <li class="page-item">
            <a class="page-link" href="{$url}&page={$lastPage}">{$lastPage}</a>
        </li>
        {/if}

        {if condition="$currentPage < $lastPage"}
        <li class="page-item">
            <a class="page-link" href="{$url}&page={$currentPage+1}" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="{$url}&page={$lastPage}" aria-label="末页">
                <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
        </li>
        {else}
        <li class="page-item disabled">
            <span class="page-link">&raquo;</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">&raquo;&raquo;</span>
        </li>
        {/if}
    </ul>
</nav>
{/if}
