<?php
// 插入测试内容数据的PHP脚本

require_once 'vendor/autoload.php';

use think\facade\Db;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    // 测试内容数据
    $contents = [
        // 基础会员内容
        [
            'title' => '基础会员使用指南',
            'content' => "欢迎使用基础会员服务！\n\n功能包括：\n1. 基础资料下载\n2. 在线学习视频\n3. 社区交流权限\n4. 30天有效期\n\n使用说明：\n- 登录后即可使用所有基础功能\n- 有效期内可无限次下载资料\n- 如有问题请联系客服",
            'category_id' => 1,
            'sort_order' => 100,
            'status' => 1
        ],
        [
            'title' => '基础会员权益说明',
            'content' => "基础会员专享权益详细说明：\n\n✓ 免费下载基础学习资料\n✓ 观看入门级教学视频\n✓ 参与新手交流群\n✓ 获得学习进度跟踪\n✓ 享受客服优先支持\n\n注意事项：\n- 会员期间请妥善保管账号信息\n- 禁止账号共享使用\n- 违规使用将被封号处理",
            'category_id' => 1,
            'sort_order' => 95,
            'status' => 1
        ],
        [
            'title' => '基础学习资料包',
            'content' => "基础会员专属学习资料包内容：\n\n📚 电子书籍：\n- 入门指南 PDF\n- 基础教程合集\n- 常见问题解答\n\n🎥 视频教程：\n- 新手入门系列（10集）\n- 基础操作演示\n- 实战案例分析\n\n📝 练习题库：\n- 基础知识测试\n- 模拟练习题\n- 答案详解",
            'category_id' => 1,
            'sort_order' => 90,
            'status' => 1
        ],
        [
            'title' => '基础会员FAQ',
            'content' => "基础会员常见问题解答：\n\nQ: 如何激活会员？\nA: 使用卡密在系统中兑换即可自动激活。\n\nQ: 会员有效期多长？\nA: 基础会员有效期为30天。\n\nQ: 可以重复激活吗？\nA: 每个卡密只能使用一次，不可重复激活。\n\nQ: 忘记密码怎么办？\nA: 请联系客服重置密码。\n\nQ: 如何下载资料？\nA: 登录后在资料中心即可下载。",
            'category_id' => 1,
            'sort_order' => 85,
            'status' => 1
        ],
        [
            'title' => '基础会员服务条款',
            'content' => "基础会员服务条款：\n\n1. 服务内容\n本服务为基础会员提供学习资料和在线教程。\n\n2. 使用规则\n- 仅限个人使用，禁止商业用途\n- 不得分享账号给他人使用\n- 不得恶意下载或传播资料\n\n3. 服务期限\n基础会员服务期限为30天，到期自动失效。\n\n4. 免责声明\n本服务仅供学习参考，不承担任何法律责任。",
            'category_id' => 1,
            'sort_order' => 80,
            'status' => 1
        ],
        
        // 高级会员内容
        [
            'title' => '高级会员专享特权',
            'content' => "高级会员尊享特权介绍：\n\n🌟 核心特权：\n- 全部学习资料免费下载\n- 高清视频教程无限观看\n- VIP专属交流群\n- 一对一答疑服务\n- 90天超长有效期\n\n🎯 专业服务：\n- 个性化学习计划\n- 进度跟踪与提醒\n- 专业导师指导\n- 实战项目参与\n- 证书认证服务",
            'category_id' => 2,
            'sort_order' => 100,
            'status' => 1
        ],
        [
            'title' => '高级学习资源库',
            'content' => "高级会员专属资源库：\n\n📖 高级教材：\n- 进阶教程全集\n- 专业技能手册\n- 行业最佳实践\n- 案例分析报告\n\n🎬 高清视频：\n- 专家讲座系列\n- 实战操作演示\n- 项目开发全程\n- 技能提升训练\n\n🛠️ 实用工具：\n- 专业软件授权\n- 开发工具包\n- 模板素材库\n- 插件扩展包",
            'category_id' => 2,
            'sort_order' => 95,
            'status' => 1
        ],
        [
            'title' => '高级会员学习路径',
            'content' => "高级会员个性化学习路径：\n\n📈 阶段一：基础巩固（1-2周）\n- 复习基础知识点\n- 完成入门项目\n- 通过基础测试\n\n📈 阶段二：技能提升（3-6周）\n- 学习高级技巧\n- 参与实战项目\n- 导师一对一指导\n\n📈 阶段三：专业认证（7-12周）\n- 完成认证项目\n- 参加专业考试\n- 获得技能证书",
            'category_id' => 2,
            'sort_order' => 90,
            'status' => 1
        ],
        [
            'title' => '高级会员专属服务',
            'content' => "高级会员专属服务详情：\n\n👨‍🏫 专业导师服务：\n- 每周定期答疑\n- 学习计划制定\n- 进度跟踪指导\n- 职业规划建议\n\n🎯 实战项目机会：\n- 真实项目参与\n- 团队协作经验\n- 作品集建设\n- 行业人脉拓展\n\n🏆 认证考试支持：\n- 考试大纲解读\n- 模拟题库练习\n- 考前冲刺辅导\n- 证书申请协助",
            'category_id' => 2,
            'sort_order' => 85,
            'status' => 1
        ],
        [
            'title' => '高级会员技术支持',
            'content' => "高级会员技术支持服务：\n\n🔧 技术支持范围：\n- 学习平台使用问题\n- 软件安装配置\n- 代码调试协助\n- 项目部署指导\n\n⏰ 支持时间：\n- 工作日：9:00-18:00\n- 响应时间：2小时内\n- 解决时间：24小时内\n\n📞 联系方式：\n- 在线客服：优先处理\n- 技术QQ群：实时交流\n- 邮件支持：详细问题\n- 电话支持：紧急情况",
            'category_id' => 2,
            'sort_order' => 80,
            'status' => 1
        ],

        // 年度会员内容
        [
            'title' => '年度会员至尊体验',
            'content' => "年度会员至尊体验介绍：\n\n👑 至尊特权：\n- 全年365天无限制访问\n- 所有付费内容免费使用\n- 新内容第一时间获取\n- 专属客服绿色通道\n- 年度学习报告定制\n\n🎁 专属福利：\n- 生日专属礼品\n- 节日特别活动\n- 线下聚会邀请\n- 行业大会门票\n- 合作伙伴优惠",
            'category_id' => 3,
            'sort_order' => 100,
            'status' => 1
        ],
        [
            'title' => '年度学习计划',
            'content' => "年度会员专属学习计划：\n\n📅 第一季度（1-3月）：\n- 基础知识体系构建\n- 核心技能掌握\n- 第一个项目完成\n\n📅 第二季度（4-6月）：\n- 进阶技能学习\n- 复杂项目挑战\n- 团队协作经验\n\n📅 第三季度（7-9月）：\n- 专业认证准备\n- 实习机会推荐\n- 作品集完善\n\n📅 第四季度（10-12月）：\n- 职业规划指导\n- 就业推荐服务\n- 年度总结评估",
            'category_id' => 3,
            'sort_order' => 95,
            'status' => 1
        ],
        [
            'title' => '年度会员专属资源',
            'content' => "年度会员专属资源库：\n\n📚 独家内容：\n- 年度更新教程\n- 行业内幕分析\n- 专家独家分享\n- 前沿技术解读\n\n🎯 定制服务：\n- 个人学习档案\n- 定制学习路径\n- 专属项目指导\n- 职业发展规划\n\n🌐 社区特权：\n- VIP专区访问\n- 专家直播互动\n- 线下活动优先\n- 人脉圈子加入",
            'category_id' => 3,
            'sort_order' => 90,
            'status' => 1
        ],
        [
            'title' => '年度会员成长体系',
            'content' => "年度会员成长体系：\n\n🏆 成长等级：\n- 青铜会员（0-3月）\n- 白银会员（3-6月）\n- 黄金会员（6-9月）\n- 钻石会员（9-12月）\n\n🎖️ 成就系统：\n- 学习时长成就\n- 项目完成成就\n- 技能认证成就\n- 社区贡献成就\n\n🎁 奖励机制：\n- 等级提升奖励\n- 成就解锁奖励\n- 月度优秀奖励\n- 年度杰出奖励",
            'category_id' => 3,
            'sort_order' => 85,
            'status' => 1
        ],
        [
            'title' => '年度会员职业服务',
            'content' => "年度会员职业发展服务：\n\n💼 职业规划：\n- 一对一职业咨询\n- 行业趋势分析\n- 技能发展建议\n- 职业路径规划\n\n🎯 就业支持：\n- 简历优化指导\n- 面试技巧培训\n- 企业内推机会\n- 薪资谈判建议\n\n🚀 创业扶持：\n- 创业项目孵化\n- 资源对接服务\n- 投资机会介绍\n- 创业导师指导",
            'category_id' => 3,
            'sort_order' => 80,
            'status' => 1
        ],

        // 终身会员内容
        [
            'title' => '终身会员无限权益',
            'content' => "终身会员无限权益说明：\n\n♾️ 永久特权：\n- 终身免费使用所有功能\n- 永久享受最新内容更新\n- 终身技术支持服务\n- 永久社区VIP身份\n- 终身学习档案保存\n\n🌟 独家待遇：\n- 产品内测优先体验\n- 重大决策意见征询\n- 年度峰会特邀嘉宾\n- 专属纪念品定制\n- 创始人直接沟通",
            'category_id' => 4,
            'sort_order' => 100,
            'status' => 1
        ],
        [
            'title' => '终身学习伙伴',
            'content' => "终身会员学习伙伴计划：\n\n🤝 伙伴关系：\n- 与平台共同成长\n- 参与产品功能设计\n- 分享学习心得体会\n- 帮助新会员成长\n\n📈 持续发展：\n- 技能持续更新\n- 知识体系完善\n- 行业洞察分享\n- 职业发展跟踪\n\n🎯 价值实现：\n- 个人品牌建设\n- 专业影响力提升\n- 商业机会获取\n- 人生价值实现",
            'category_id' => 4,
            'sort_order' => 95,
            'status' => 1
        ],
        [
            'title' => '终身会员传承计划',
            'content' => "终身会员知识传承计划：\n\n📖 知识传承：\n- 经验分享平台\n- 导师培养计划\n- 知识库建设\n- 文化传承使命\n\n👥 社区建设：\n- 核心成员培养\n- 社区文化塑造\n- 新人引导机制\n- 活动组织策划\n\n🌱 生态发展：\n- 产业链条完善\n- 合作伙伴拓展\n- 创新项目孵化\n- 可持续发展",
            'category_id' => 4,
            'sort_order' => 90,
            'status' => 1
        ],

        // 企业版内容
        [
            'title' => '企业版解决方案',
            'content' => "企业版全方位解决方案：\n\n🏢 企业服务：\n- 多用户账号管理\n- 团队协作平台\n- 企业级安全保障\n- 定制化功能开发\n- 专属客户经理\n\n📊 管理功能：\n- 员工学习进度跟踪\n- 培训效果评估\n- 数据统计分析\n- 报告自动生成\n- 权限分级管理\n\n🎯 培训体系：\n- 企业内训定制\n- 岗位技能培训\n- 管理能力提升\n- 团队建设活动\n- 绩效考核支持",
            'category_id' => 5,
            'sort_order' => 100,
            'status' => 1
        ],
        [
            'title' => '企业版管理平台',
            'content' => "企业版管理平台功能：\n\n👨‍💼 管理员功能：\n- 员工账号批量管理\n- 学习内容分配\n- 进度监控统计\n- 考试成绩管理\n- 证书颁发审核\n\n📈 数据分析：\n- 学习数据可视化\n- 培训ROI分析\n- 员工能力评估\n- 部门对比报告\n- 趋势预测分析\n\n🔒 安全保障：\n- 企业级数据加密\n- 访问权限控制\n- 操作日志记录\n- 数据备份恢复\n- 合规性审计",
            'category_id' => 5,
            'sort_order' => 95,
            'status' => 1
        ],
        [
            'title' => '企业版定制服务',
            'content' => "企业版定制服务内容：\n\n🎨 界面定制：\n- 企业品牌元素集成\n- 个性化界面设计\n- 功能模块定制\n- 工作流程优化\n\n🔧 功能开发：\n- 专属功能模块\n- 第三方系统集成\n- API接口开发\n- 移动端应用\n\n📞 专属支持：\n- 7×24小时技术支持\n- 专属客户经理\n- 定期巡检服务\n- 紧急响应机制",
            'category_id' => 5,
            'sort_order' => 90,
            'status' => 1
        ]
    ];

    // 批量插入数据
    foreach ($contents as $content) {
        Db::name('km_contents')->insert($content);
    }

    echo "成功插入 " . count($contents) . " 条测试内容数据！\n";

} catch (Exception $e) {
    echo "插入数据失败：" . $e->getMessage() . "\n";
}
