/**
 * 侧边栏布局样式
 * 统一的侧边栏导航和布局样式
 */

/* 侧边栏容器 */
.sidebar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  z-index: var(--z-fixed);
  width: var(--sidebar-width);
  height: calc(100vh - var(--header-height));
  background: var(--white);
  border-right: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow);
  overflow-y: auto;
  overflow-x: hidden;
  transition: all var(--transition-normal);
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar.hidden {
  transform: translateX(-100%);
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--spacing-4);
  border-bottom: var(--border-width) solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  position: sticky;
  top: 0;
  z-index: 10;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  color: var(--white);
  text-decoration: none;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.sidebar-brand:hover {
  color: var(--white);
  text-decoration: none;
}

.sidebar-brand-icon {
  width: 32px;
  height: 32px;
  margin-right: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
}

.sidebar-brand-text {
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-brand-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 侧边栏导航 */
.sidebar-nav {
  padding: var(--spacing-4) 0;
}

.sidebar-nav-section {
  margin-bottom: var(--spacing-6);
}

.sidebar-nav-title {
  padding: 0 var(--spacing-4);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-nav-title {
  opacity: 0;
  height: 0;
  margin: 0;
  overflow: hidden;
}

.sidebar-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-nav-item {
  margin-bottom: var(--spacing-1);
}

/* 导航链接 */
.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0;
  margin-right: var(--spacing-4);
  position: relative;
}

.sidebar-nav-link:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
  text-decoration: none;
  transform: translateX(4px);
}

.sidebar-nav-link.active {
  color: var(--primary-color);
  background-color: var(--primary-light);
  font-weight: var(--font-weight-medium);
}

.sidebar-nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color);
  border-radius: 0 2px 2px 0;
}

.sidebar-nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.sidebar-nav-text {
  flex: 1;
  transition: opacity var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-nav-text {
  opacity: 0;
  width: 0;
}

.sidebar-nav-badge {
  margin-left: auto;
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-nav-badge {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 子菜单 */
.sidebar-nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.sidebar-nav-item.expanded .sidebar-nav-submenu {
  max-height: 500px;
}

.sidebar-nav-submenu .sidebar-nav-link {
  padding-left: var(--spacing-12);
  font-size: var(--font-size-sm);
}

.sidebar-nav-submenu .sidebar-nav-icon {
  width: 16px;
  height: 16px;
  font-size: var(--font-size-sm);
}

.sidebar-nav-toggle {
  margin-left: auto;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast);
}

.sidebar-nav-item.expanded .sidebar-nav-toggle {
  transform: rotate(90deg);
}

.sidebar.collapsed .sidebar-nav-toggle {
  opacity: 0;
  width: 0;
}

/* 侧边栏底部 */
.sidebar-footer {
  position: sticky;
  bottom: 0;
  padding: var(--spacing-4);
  border-top: var(--border-width) solid var(--border-color);
  background: var(--white);
}

.sidebar-user {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--gray-100);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
}

.sidebar-user:hover {
  background: var(--gray-200);
}

.sidebar-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-full);
  margin-right: var(--spacing-3);
  object-fit: cover;
  flex-shrink: 0;
}

.sidebar-user-info {
  flex: 1;
  min-width: 0;
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-user-info {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-user-role {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 折叠按钮 */
.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 10;
}

.sidebar-toggle:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.sidebar-toggle-icon {
  font-size: var(--font-size-sm);
  transition: transform var(--transition-fast);
}

.sidebar.collapsed .sidebar-toggle-icon {
  transform: rotate(180deg);
}

/* 工具提示（折叠状态下显示） */
.sidebar-tooltip {
  position: absolute;
  left: calc(100% + var(--spacing-2));
  top: 50%;
  transform: translateY(-50%);
  background: var(--gray-800);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: var(--z-tooltip);
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: var(--gray-800);
}

.sidebar.collapsed .sidebar-nav-link:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: var(--z-modal);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .sidebar-toggle {
    display: none;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}
