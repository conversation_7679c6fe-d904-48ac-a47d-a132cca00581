{extend name="layout/base" /}

{block name="title"}卡密管理 - 卡密兑换管理系统{/block}

{block name="style"}
<style>
    .filter-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .table-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .btn-group .btn {
        margin-right: 8px;
    }

    .table {
        margin: 0;
    }

    .table th {
        background-color: var(--content-bg);
        border: none;
        font-weight: 600;
        color: var(--text-primary);
        padding: 16px 12px;
        font-size: 14px;
    }

    .table td {
        border: none;
        padding: 16px 12px;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        font-size: 14px;
    }

    .table tbody tr:hover {
        background-color: var(--content-bg);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-unused {
        background-color: #fff7e6;
        color: #d46b08;
        border: 1px solid #ffd591;
    }

    .status-used {
        background-color: #f6ffed;
        color: #389e0d;
        border: 1px solid #b7eb8f;
    }

    .status-disabled {
        background-color: #fff2f0;
        color: #cf1322;
        border: 1px solid #ffccc7;
    }
    
    .card-code {
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        background-color: var(--content-bg);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid var(--border-color);
    }

    .pagination {
        justify-content: center;
        margin-top: 24px;
    }
    
    .filter-form .form-control,
    .filter-form .form-select {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    /* 紧凑型筛选样式 */
    .compact-filter .modern-card-header {
        padding: 8px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .compact-filter .modern-card-body {
        padding: 12px 16px;
    }

    .compact-filter .modern-form-control {
        height: 36px;
        font-size: 14px;
        padding: 6px 12px;
    }

    .compact-filter .modern-btn {
        height: 36px;
        padding: 6px 16px;
        font-size: 14px;
    }

    .compact-filter .modern-card-title {
        font-size: 14px;
        font-weight: 600;
    }

    /* 分页样式 - 适配ThinkPHP默认分页结构 */
    .pagination-wrapper .pagination {
        margin: 0;
        display: flex;
        list-style: none;
        padding: 0;
        gap: 4px;
    }

    .pagination-wrapper .pagination li {
        display: inline-block;
    }

    .pagination-wrapper .pagination li a,
    .pagination-wrapper .pagination li span {
        display: inline-block;
        color: var(--text-primary);
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        padding: 8px 12px;
        font-size: 14px;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
        text-align: center;
        line-height: 1.2;
    }

    .pagination-wrapper .pagination li a:hover {
        color: var(--primary-color);
        background-color: rgba(99, 102, 241, 0.1);
        border-color: var(--primary-color);
        text-decoration: none;
    }

    .pagination-wrapper .pagination li.active span {
        color: white;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        font-weight: 600;
    }

    .pagination-wrapper .pagination li.disabled span {
        color: var(--text-muted);
        background-color: var(--card-bg);
        border-color: var(--border-color);
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 响应式分页 */
    @media (max-width: 768px) {
        .pagination-wrapper .pagination li a,
        .pagination-wrapper .pagination li span {
            padding: 6px 10px;
            font-size: 13px;
            min-width: 36px;
        }

        .pagination-wrapper {
            overflow-x: auto;
        }

        .pagination-wrapper .pagination {
            white-space: nowrap;
        }
    }
    
    @media (max-width: 768px) {
        .table-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .btn-group {
            flex-wrap: wrap;
        }
        
        .filter-form {
            flex-direction: column;
        }
        
        .filter-form .form-control,
        .filter-form .form-select {
            margin-right: 0;
            width: 100%;
        }
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 16px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .modal-header h2 i {
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 32px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 20px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    /* 生成说明样式 */
    .generate-tips {
        background: #f0f9ff;
        border-left: 4px solid var(--primary-color);
        padding: 16px;
        border-radius: 0 6px 6px 0;
        margin-bottom: 24px;
        border: 1px solid #bae7ff;
        border-left: 4px solid var(--primary-color);
    }

    .generate-tips h6 {
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
    }

    .generate-tips ul {
        margin: 0;
        padding-left: 20px;
    }

    .generate-tips li {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 4px;
        line-height: 1.5;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .modal .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .modal .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 分类树形选择器样式 */
    .category-tree-selector {
        position: relative;
        z-index: 10000;
    }



    .tree-selector-input {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        padding: 0.75rem 1rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 48px;
    }

    .tree-selector-input:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .tree-selector-input.active {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .selected-text {
        color: var(--text-primary);
        font-size: 0.875rem;
        flex: 1;
    }

    .dropdown-arrow {
        color: var(--text-secondary);
        transition: transform 0.3s ease;
        font-size: 0.75rem;
    }

    .tree-selector-input.active .dropdown-arrow {
        transform: rotate(180deg);
    }

    .tree-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 9999;
        margin-top: 4px;
    }

    .tree-item {
        position: relative;
    }

    .tree-item-content {
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .tree-item-content:hover {
        background: rgba(99, 102, 241, 0.05);
    }

    .tree-item.selected > .tree-item-content {
        background: rgba(99, 102, 241, 0.1);
        color: var(--primary-color);
        font-weight: 500;
    }

    .expand-icon {
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s ease;
        color: var(--text-secondary);
        font-size: 0.75rem;
    }

    .expand-icon.expanded {
        transform: rotate(90deg);
    }

    .expand-icon-placeholder {
        width: 12px;
        height: 12px;
    }

    .category-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        font-size: 8px;
    }

    .level-1-icon {
        background: #e3f2fd;
        color: #1976d2;
    }

    .level-2-icon {
        background: #e8f5e8;
        color: #4caf50;
    }

    .level-3-icon {
        background: #f3e5f5;
        color: #9c27b0;
    }

    .tree-item.level-1 > .tree-item-content {
        padding-left: 0.75rem;
    }

    .tree-item.level-2 > .tree-item-content {
        padding-left: 1.5rem;
    }

    .tree-item.level-3 > .tree-item-content {
        padding-left: 2.25rem;
        font-size: 0.875rem;
    }

    .tree-children {
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .tree-children.expanded {
        display: block !important;
    }

    /* 按钮样式 */
    .btn-primary, .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .btn-primary:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    /* Toast 动画 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modal-content {
            margin: 10% auto;
            width: 95%;
            max-height: 85vh;
        }

        .modal-header {
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            flex-direction: column;
        }
    }
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">卡密管理</h1>
        <p class="text-muted mb-0">管理和监控所有卡密的状态</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button class="modern-btn modern-btn-success" onclick="exportSelected()">
            <i class="fas fa-download"></i>
            导出
        </button>
        <a href="/card/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 筛选条件 -->
<div class="modern-card mb-3 compact-filter">
    <div class="modern-card-header">
        <h6 class="modern-card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h6>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-2 align-items-center">
                <div class="col-md-2">
                    <select name="status" class="modern-form-control">
                        <option value="">全部状态</option>
                        <option value="0" {$filters.status == '0' ? 'selected' : ''}>未使用</option>
                        <option value="1" {$filters.status == '1' ? 'selected' : ''}>已使用</option>
                        <option value="2" {$filters.status == '2' ? 'selected' : ''}>已禁用</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <div class="category-tree-selector">
                        <div class="tree-selector-input" onclick="toggleCategoryDropdown()">
                            <span class="selected-text" id="selectedCategoryText">
                                {if condition="$filters.category_id"}
                                    {php}
                                        function findCategoryById($categories, $id) {
                                            foreach ($categories as $category) {
                                                if ($category['id'] == $id) {
                                                    return $category;
                                                }
                                                if (!empty($category['children'])) {
                                                    $found = findCategoryById($category['children'], $id);
                                                    if ($found) return $found;
                                                }
                                            }
                                            return null;
                                        }
                                        $selectedCategory = findCategoryById($categories, $filters['category_id']);
                                        echo $selectedCategory ? $selectedCategory['name'] : '全部分类';
                                    {/php}
                                {else}
                                    全部分类
                                {/if}
                            </span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="categoryTreeDropdown" style="display: none;">
                            <div class="tree-item" data-id="" onclick="selectCategory('', '全部分类')">
                                <i class="fas fa-list text-primary"></i>
                                <span>全部分类</span>
                            </div>
                            {volist name="categories" id="category"}
                                <div class="tree-item level-1 {if condition="!empty($category.children)"}has-children{/if}" data-id="{$category.id}">
                                    <div class="tree-item-content" onclick="selectCategory('{$category.id}', '{$category.name}')">
                                        {if condition="!empty($category.children)"}
                                            <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                        {else}
                                            <span class="expand-icon-placeholder"></span>
                                        {/if}
                                        <div class="category-icon level-1-icon">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <span>{$category.name}</span>
                                    </div>
                                    {if condition="!empty($category.children)"}
                                        <div class="tree-children" style="display: none;">
                                            {volist name="category.children" id="child"}
                                                <div class="tree-item level-2 {if condition="!empty($child.children)"}has-children{/if}" data-id="{$child.id}">
                                                    <div class="tree-item-content" onclick="selectCategory('{$child.id}', '{$child.name}')">
                                                        {if condition="!empty($child.children)"}
                                                            <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                                        {else}
                                                            <span class="expand-icon-placeholder"></span>
                                                        {/if}
                                                        <div class="category-icon level-2-icon">
                                                            <i class="fas fa-square"></i>
                                                        </div>
                                                        <span>{$child.name}</span>
                                                    </div>
                                                    {if condition="!empty($child.children)"}
                                                        <div class="tree-children" style="display: none;">
                                                            {volist name="child.children" id="grandchild"}
                                                                <div class="tree-item level-3" data-id="{$grandchild.id}">
                                                                    <div class="tree-item-content" onclick="selectCategory('{$grandchild.id}', '{$grandchild.name}')">
                                                                        <span class="expand-icon-placeholder"></span>
                                                                        <div class="category-icon level-3-icon">
                                                                            <i class="fas fa-circle"></i>
                                                                        </div>
                                                                        <span>{$grandchild.name}</span>
                                                                    </div>
                                                                </div>
                                                            {/volist}
                                                        </div>
                                                    {/if}
                                                </div>
                                            {/volist}
                                        </div>
                                    {/if}
                                </div>
                            {/volist}
                        </div>
                        <input type="hidden" name="category_id" id="categoryIdInput" value="{$filters.category_id}">
                    </div>
                </div>

                <div class="col-md-4">
                    <input type="text" name="keyword" class="modern-form-control" placeholder="搜索卡密编号" value="{$filters.keyword}">
                </div>

                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <a href="/cards" class="modern-btn modern-btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 卡密列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            卡密列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" id="selectAllBtn" class="modern-btn modern-btn-outline btn-sm" onclick="toggleSelectAllBtn()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="showGenerateModal()">
                <i class="fas fa-plus"></i>
                生成卡密
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="batchAction('disable')">
                <i class="fas fa-ban"></i>
                批量禁用
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="showExportModal()">
                <i class="fas fa-download"></i>
                导出
            </button>
        </div>
    </div>

    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
            <thead>
                <tr>
                    <th width="50">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    </th>
                    <th>卡密编号</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>内容</th>
                    <th>创建时间</th>
                    <th>使用时间</th>
                    <th>过期时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {volist name="cards" id="card"}
                <tr>
                    <td>
                        <input type="checkbox" class="card-checkbox" value="{$card.id}">
                    </td>
                    <td>
                        <code class="text-primary">{$card.card_code}</code>
                    </td>
                    <td>
                        <span class="modern-badge modern-badge-primary">{$card.category_name}</span>
                    </td>
                    <td>
                        {switch name="card.status"}
                            {case value="0"}
                                <span class="modern-badge modern-badge-warning">
                                    <i class="fas fa-clock"></i>
                                    未使用
                                </span>
                            {/case}
                            {case value="1"}
                                <span class="modern-badge modern-badge-success">
                                    <i class="fas fa-check-circle"></i>
                                    已使用
                                </span>
                            {/case}
                            {case value="2"}
                                <span class="modern-badge modern-badge-error">
                                    <i class="fas fa-ban"></i>
                                    已禁用
                                </span>
                            {/case}
                        {/switch}
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="{$card.content}">
                            {$card.content}
                        </div>
                    </td>
                    <td class="text-muted">{$card.created_at}</td>
                    <td class="text-muted">{$card.used_at ?: '-'}</td>
                    <td class="text-muted">{$card.expire_at ?: '-'}</td>
                    <td>
                        <div class="d-flex gap-1">
                            {if $card.status != 1}
                            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="updateStatus({$card.id}, {$card.status == 0 ? 2 : 0})" title="{$card.status == 0 ? '禁用' : '启用'}">
                                <i class="fas fa-{$card.status == 0 ? 'ban' : 'check'}"></i>
                            </button>
                            {/if}
                            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="editCard({$card.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {/volist}
                {empty name="cards"}
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                            <div>暂无卡密数据</div>
                            <div class="mt-2">
                                <button class="modern-btn modern-btn-primary" onclick="showGenerateModal()">
                                    <i class="fas fa-plus"></i>
                                    立即生成
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
                {/empty}
            </tbody>
        </table>
        </div>
    </div>

    <!-- 分页 -->
    <div class="modern-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <div class="text-muted">
                    显示第 {$cards->currentPage()} 页，共 {$cards->lastPage()} 页，总计 {$cards->total()} 条记录
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="text-muted small">每页显示:</span>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                        <option value="5" {$filters.limit == 5 ? 'selected' : ''}>5条</option>
                        <option value="10" {$filters.limit == 10 ? 'selected' : ''}>10条</option>
                        <option value="20" {$filters.limit == 20 ? 'selected' : ''}>20条</option>
                        <option value="50" {$filters.limit == 50 ? 'selected' : ''}>50条</option>
                        <option value="100" {$filters.limit == 100 ? 'selected' : ''}>100条</option>
                    </select>
                </div>
            </div>
            <div class="pagination-wrapper">
                {if condition="$cards->hasPages()"}
                    {$cards->appends(request()->param())->render()|raw}
                {/if}
            </div>
        </div>
    </div>
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出卡密</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportStatus" class="form-label">状态筛选</label>
                        <select class="form-select" id="exportStatus">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                            <option value="2">已禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exportCategory" class="form-label">分类筛选</label>
                        <select class="form-select" id="exportCategory">
                            <option value="">全部分类</option>
                            {volist name="categories" id="category"}
                            <option value="{$category.id}">{$category.name}</option>
                            {/volist}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportCards()">导出</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    // 全选/取消全选 - 表格头部复选框
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.card-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        updateSelectAllBtn();
    }

    // 全选/取消全选 - 按钮
    function toggleSelectAllBtn() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.card-checkbox');

        const isCurrentlyAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        if (isCurrentlyAllSelected) {
            // 当前全选状态，点击后取消全选
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
            selectAllBtn.classList.remove('modern-btn-success');
            selectAllBtn.classList.add('modern-btn-outline');
        } else {
            // 当前非全选状态，点击后全选
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            selectAllCheckbox.checked = true;
            selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i> 取消全选';
            selectAllBtn.classList.remove('modern-btn-outline');
            selectAllBtn.classList.add('modern-btn-success');
        }
    }

    // 更新全选按钮状态
    function updateSelectAllBtn() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const checkboxes = document.querySelectorAll('.card-checkbox');
        const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');

        if (checkboxes.length === 0) {
            return;
        }

        if (checkedBoxes.length === checkboxes.length) {
            // 全选状态
            selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i> 取消全选';
            selectAllBtn.classList.remove('modern-btn-outline');
            selectAllBtn.classList.add('modern-btn-success');
        } else {
            // 非全选状态
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
            selectAllBtn.classList.remove('modern-btn-success');
            selectAllBtn.classList.add('modern-btn-outline');
        }
    }

    // 监听单个复选框变化
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.card-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectAllCheckbox = document.getElementById('selectAll');
                const allCheckboxes = document.querySelectorAll('.card-checkbox');
                const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');

                // 更新表格头部的全选复选框状态
                selectAllCheckbox.checked = checkedBoxes.length === allCheckboxes.length;

                // 更新全选按钮状态
                updateSelectAllBtn();
            });
        });

        // 初始化按钮状态
        updateSelectAllBtn();
    });

    // 改变每页显示数量
    function changePageSize(limit) {
        const url = new URL(window.location);
        url.searchParams.set('limit', limit);
        url.searchParams.set('page', 1); // 重置到第一页
        window.location.href = url.toString();
    }
    
    // 更新卡密状态
    function updateStatus(id, status) {
        if (!confirm('确定要修改此卡密的状态吗？')) {
            return;
        }
        
        fetch('/cards/updateStatus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id, status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('状态更新成功');
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 批量操作
    function batchAction(action) {
        const checkboxes = document.querySelectorAll('.card-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('请选择要操作的卡密');
            return;
        }

        const ids = Array.from(checkboxes).map(cb => cb.value);
        const actionText = action === 'delete' ? '删除' : '禁用';

        if (!confirm(`确定要${actionText}选中的 ${ids.length} 个卡密吗？`)) {
            return;
        }

        const url = '/cards/batchDelete';

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 显示导出模态框
    function showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }
    
    // 导出卡密
    function exportCards() {
        const password = document.getElementById('exportPassword').value;
        const status = document.getElementById('exportStatus').value;
        const categoryId = document.getElementById('exportCategory').value;
        
        if (!password) {
            alert('请输入导出密码');
            return;
        }
        
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/cards/export';
        
        const fields = {
            password: password,
            status: status,
            category_id: categoryId
        };
        
        for (const key in fields) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = fields[key];
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();
    }

    // 显示生成卡密模态框
    function showGenerateModal() {
        // 先获取分类数据
        fetch('/cards/getCategories')
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    populateCategories(data.data);
                    document.getElementById('generateModal').style.display = 'block';
                    document.body.style.overflow = 'hidden';

                    // 设置默认过期时间为30天后
                    setDefaultExpireTime();
                } else {
                    showToast('获取分类数据失败', 'error');
                }
            })
            .catch(error => {
                showToast('获取分类数据失败', 'error');
            });
    }

    // 关闭生成卡密模态框
    function closeGenerateModal() {
        document.getElementById('generateModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        document.getElementById('generateForm').reset();
    }

    // 填充分类选项
    function populateCategories(categories) {
        const select = document.getElementById('generate_category_id');
        select.innerHTML = '<option value="">请选择分类</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.className = `category-option-level-${category.level}`;

            let prefix = '';
            switch(category.level) {
                case 1:
                    prefix = '■ ';
                    break;
                case 2:
                    prefix = '    ▶ ';
                    break;
                case 3:
                    prefix = '        ● ';
                    break;
            }

            option.textContent = prefix + category.name;
            select.appendChild(option);
        });
    }

    // 设置默认过期时间
    function setDefaultExpireTime() {
        const expireInput = document.getElementById('generate_expire_at');
        const now = new Date();
        now.setDate(now.getDate() + 30);

        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        expireInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 提交生成表单
    function submitGenerate() {
        const form = document.getElementById('generateForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // 验证表单
        if (!data.category_id || !data.count || !data.content) {
            showToast('请填写所有必填项', 'warning');
            return;
        }

        const count = parseInt(data.count);
        if (count < 1 || count > 1000) {
            showToast('生成数量必须在1-1000之间', 'warning');
            return;
        }

        // 显示加载状态
        setGenerateLoading(true);

        // 发送请求
        fetch('/cards/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showToast(result.message, 'success');
                closeGenerateModal();
                // 刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(result.message, 'error');
            }
        })
        .catch(error => {
            showToast('生成失败，请稍后重试', 'error');
        })
        .finally(() => {
            setGenerateLoading(false);
        });
    }

    // 设置生成按钮加载状态
    function setGenerateLoading(loading) {
        const btn = document.getElementById('generateSubmitBtn');
        const btnText = btn.querySelector('.btn-text');
        const loadingSpinner = btn.querySelector('.loading-spinner');

        if (loading) {
            btnText.style.display = 'none';
            loadingSpinner.style.display = 'inline-block';
            btn.disabled = true;
        } else {
            btnText.style.display = 'inline-block';
            loadingSpinner.style.display = 'none';
            btn.disabled = false;
        }
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const generateModal = document.getElementById('generateModal');
        if (event.target === generateModal) {
            closeGenerateModal();
        }
    }

    // 分类树形选择器功能
    function toggleCategoryDropdown() {
        const dropdown = document.getElementById('categoryTreeDropdown');
        const input = document.querySelector('.tree-selector-input');

        if (dropdown.style.display === 'none') {
            dropdown.style.display = 'block';
            input.classList.add('active');
        } else {
            dropdown.style.display = 'none';
            input.classList.remove('active');
        }
    }

    function selectCategory(categoryId, categoryName) {
        // 更新显示文本
        document.getElementById('selectedCategoryText').textContent = categoryName;

        // 更新隐藏输入框的值
        document.getElementById('categoryIdInput').value = categoryId;

        // 更新选中状态
        document.querySelectorAll('.tree-item').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedItem = document.querySelector(`[data-id="${categoryId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }

        // 关闭下拉框
        document.getElementById('categoryTreeDropdown').style.display = 'none';
        document.querySelector('.tree-selector-input').classList.remove('active');
    }

    function toggleTreeNode(expandIcon) {
        const treeItem = expandIcon.closest('.tree-item');
        const children = treeItem.querySelector('.tree-children');

        if (children) {
            if (children.style.display === 'none') {
                children.style.display = 'block';
                expandIcon.classList.add('expanded');
            } else {
                children.style.display = 'none';
                expandIcon.classList.remove('expanded');
            }
        }
    }

    // 点击外部关闭下拉框
    document.addEventListener('click', function(event) {
        const selector = document.querySelector('.category-tree-selector');
        if (selector && !selector.contains(event.target)) {
            document.getElementById('categoryTreeDropdown').style.display = 'none';
            document.querySelector('.tree-selector-input').classList.remove('active');
        }
    });

    // 页面加载时设置当前选中的分类
    document.addEventListener('DOMContentLoaded', function() {
        const currentCategoryId = document.getElementById('categoryIdInput').value;
        if (currentCategoryId) {
            const selectedItem = document.querySelector(`[data-id="${currentCategoryId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');

                // 展开父级节点
                let parent = selectedItem.parentElement;
                while (parent && parent.classList.contains('tree-children')) {
                    parent.style.display = 'block';
                    const parentItem = parent.parentElement;
                    const expandIcon = parentItem.querySelector('.expand-icon');
                    if (expandIcon) {
                        expandIcon.classList.add('expanded');
                    }
                    parent = parentItem.parentElement;
                }
            }
        }
    });
</script>

<!-- 生成卡密模态框 -->
<div id="generateModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2><i class="fas fa-magic"></i> 生成卡密</h2>
            <span class="close" onclick="closeGenerateModal()">&times;</span>
        </div>
        <div class="modal-body">
            <!-- 生成说明 -->
            <div class="generate-tips">
                <h6><i class="fas fa-info-circle"></i> 生成说明</h6>
                <ul>
                    <li>每次最多可生成1000个卡密</li>
                    <li>卡密编号将自动生成，确保唯一性</li>
                    <li>过期时间为可选项，不设置则永不过期</li>
                    <li>生成后的卡密状态为"未使用"</li>
                </ul>
            </div>

            <!-- 生成表单 -->
            <form id="generateForm">
                <div class="form-group">
                    <label for="generate_category_id">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <select id="generate_category_id" name="category_id" class="form-control" required>
                        <option value="">请选择分类</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="generate_count">生成数量 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="generate_count" name="count" class="form-control"
                           min="1" max="1000" value="10" required>
                    <small class="form-text">请输入1-1000之间的数字</small>
                </div>

                <div class="form-group">
                    <label for="generate_content">兑换内容 <span style="color: #ff4d4f;">*</span></label>
                    <textarea id="generate_content" name="content" class="form-control"
                              rows="4" placeholder="请输入卡密兑换后显示的内容..." required></textarea>
                    <small class="form-text">用户兑换卡密后将看到此内容</small>
                </div>

                <div class="form-group">
                    <label for="generate_expire_at">过期时间</label>
                    <input type="datetime-local" id="generate_expire_at" name="expire_at" class="form-control">
                    <small class="form-text">不设置则永不过期</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeGenerateModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="generateSubmitBtn" class="btn-primary" onclick="submitGenerate()">
                <span class="btn-text">
                    <i class="fas fa-magic"></i> 开始生成
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 生成中...
                </span>
            </button>
        </div>
    </div>
</div>

{/block}
