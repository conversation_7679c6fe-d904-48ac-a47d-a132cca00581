/**
 * 主体布局样式
 * 统一的页面布局和容器样式
 */

/* 页面容器 */
.page-wrapper {
  min-height: 100vh;
  background-color: var(--gray-100);
}

.page-content {
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
  transition: margin-left var(--transition-normal);
}

.sidebar.collapsed + .page-content {
  margin-left: 60px;
}

.sidebar.hidden + .page-content {
  margin-left: 0;
}

/* 主要内容区域 */
.main-content {
  padding: var(--spacing-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.main-content-full {
  padding: var(--spacing-6);
  width: 100%;
}

.main-content-compact {
  padding: var(--spacing-4);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  background: var(--white);
  border-bottom: var(--border-width) solid var(--border-color);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.page-header-content {
  max-width: var(--container-max-width);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin: 0;
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  margin-top: var(--spacing-2);
  margin-bottom: 0;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* 页面主体 */
.page-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 内容区域 */
.content-area {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.content-header {
  padding: var(--spacing-5) var(--spacing-6);
  border-bottom: var(--border-width) solid var(--border-color);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.content-body {
  padding: var(--spacing-6);
}

.content-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: var(--border-width) solid var(--border-color);
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
.grid-5 { grid-template-columns: repeat(5, 1fr); }
.grid-6 { grid-template-columns: repeat(6, 1fr); }

/* 响应式网格 */
.grid-responsive {
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .grid-responsive-sm-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-responsive-sm-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
  .grid-responsive-md-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-responsive-md-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-responsive-md-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 992px) {
  .grid-responsive-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-responsive-lg-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-responsive-lg-5 { grid-template-columns: repeat(5, 1fr); }
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

/* 对齐方式 */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* 间距 */
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* 容器 */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-4);
}

.container-sm {
  max-width: 576px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 992px;
}

.container-xl {
  max-width: 1200px;
}

/* 分栏布局 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-3) * -1);
}

.col {
  flex: 1 0 0%;
  padding: 0 var(--spacing-3);
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 页脚 */
.page-footer {
  background: var(--white);
  border-top: var(--border-width) solid var(--border-color);
  padding: var(--spacing-6);
  margin-top: auto;
}

.page-footer-content {
  max-width: var(--container-max-width);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.page-footer-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.page-footer-link {
  color: var(--gray-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.page-footer-link:hover {
  color: var(--primary-color);
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    margin-left: 0;
  }
  
  .main-content {
    padding: var(--spacing-4);
  }
  
  .page-header {
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
  }
  
  .page-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }
  
  .page-title {
    font-size: var(--font-size-2xl);
  }
  
  .content-header {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .content-body {
    padding: var(--spacing-4);
  }
  
  .content-footer {
    padding: var(--spacing-3) var(--spacing-4);
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .grid-2,
  .grid-3,
  .grid-4,
  .grid-5,
  .grid-6 {
    grid-template-columns: 1fr;
  }
  
  .page-footer-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: var(--spacing-3);
  }
  
  .page-header {
    padding: var(--spacing-3);
    margin-bottom: var(--spacing-3);
  }
  
  .content-header {
    padding: var(--spacing-3);
  }
  
  .content-body {
    padding: var(--spacing-3);
  }
  
  .container {
    padding: 0 var(--spacing-3);
  }
  
  .row {
    margin: 0 calc(var(--spacing-2) * -1);
  }
  
  .col {
    padding: 0 var(--spacing-2);
  }
}
