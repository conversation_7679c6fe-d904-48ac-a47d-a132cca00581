<?php

namespace app\controller;

use app\BaseController;
use app\model\Content as ContentModel;
use app\model\Category;
use app\model\Card;

/**
 * 内容管理控制器
 */
class Content extends BaseController
{
    /**
     * 内容管理首页
     */
    public function index()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $status = $this->request->param('status', '');
        $keyword = $this->request->param('keyword', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['c.category_id', '=', $category_id];
        }
        if ($status !== '') {
            $where[] = ['c.status', '=', $status];
        }
        if ($keyword) {
            $where[] = ['c.title|c.content', 'like', '%' . $keyword . '%'];
        }

        // 查询内容列表
        $contents = ContentModel::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.id',
                'c.title',
                'c.content',
                'c.category_id',
                'c.sort_order',
                'c.status',
                'c.created_at',
                'c.updated_at',
                'cat.name as category_name'
            ])
            ->where($where)
            ->order('c.sort_order', 'desc')
            ->order('c.created_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 为每个内容添加完整的分类路径
        foreach ($contents as $content) {
            $content->category_path = ContentModel::getCategoryPath($content->category_id);
        }

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        // 获取统计数据
        $stats = ContentModel::getStats();

        return view('content/index', [
            'contents' => $contents,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'category_id' => $category_id,
                'keyword' => $keyword
            ]
        ]);
    }

    /**
     * 创建内容页面
     */
    public function create()
    {
        if ($this->request->isPost()) {
            return $this->save();
        }

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        return view('content/create', [
            'categories' => $categories
        ]);
    }

    /**
     * 编辑内容页面
     */
    public function edit()
    {
        $id = $this->request->param('id');
        
        if ($this->request->isPost()) {
            return $this->save();
        }

        $content = ContentModel::find($id);
        if (!$content) {
            $this->error('内容不存在');
        }

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        return view('content/edit', [
            'content' => $content,
            'categories' => $categories
        ]);
    }

    /**
     * 保存内容
     */
    public function save()
    {
        $data = $this->request->post();

        // 验证数据
        $validate = $this->validate($data, [
            'title' => 'require|max:255',
            'content' => 'require|max:10000',
            'category_id' => 'require|integer',
            'sort_order' => 'integer',
            'status' => 'in:0,1'
        ]);

        if ($validate !== true) {
            return json(['code' => 400, 'message' => $validate]);
        }

        try {
            if (isset($data['id']) && $data['id']) {
                // 更新
                $content = ContentModel::find($data['id']);
                if (!$content) {
                    return json(['code' => 404, 'message' => '内容不存在']);
                }
                $isUpdate = true;
            } else {
                // 新增
                $content = new ContentModel();
                $isUpdate = false;
            }

            $content->title = trim($data['title']);
            $content->content = trim($data['content']);
            $content->category_id = $data['category_id'];
            $content->sort_order = $data['sort_order'] ?? 0;
            $content->status = $data['status'] ?? ContentModel::STATUS_ENABLED;
            $content->save();

            $message = $isUpdate ? '内容更新成功' : '内容创建成功';
            return json(['code' => 200, 'message' => $message]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除内容
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $id = $this->request->post('id');
        
        if (!$id) {
            return json(['code' => 400, 'message' => '参数错误']);
        }

        try {
            $content = ContentModel::find($id);
            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            // 检查是否有关联的卡密
            $cardCount = Card::where('content_id', $id)->count();
            if ($cardCount > 0) {
                return json(['code' => 400, 'message' => '该内容已被卡密使用，无法删除']);
            }

            $content->delete();

            return json(['code' => 200, 'message' => '删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换状态
     */
    public function toggleStatus()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $id = $this->request->post('id');
        
        if (!$id) {
            return json(['code' => 400, 'message' => '参数错误']);
        }

        try {
            $content = ContentModel::find($id);
            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            $content->status = $content->status == ContentModel::STATUS_ENABLED ? ContentModel::STATUS_DISABLED : ContentModel::STATUS_ENABLED;
            $content->save();

            $statusText = $content->status == ContentModel::STATUS_ENABLED ? '启用' : '禁用';
            return json(['code' => 200, 'message' => "状态已更新为：{$statusText}"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '状态更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取内容数据（用于编辑模态框）
     */
    public function getData()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 400, 'message' => '参数错误']);
        }

        try {
            $content = ContentModel::alias('c')
                ->leftJoin('km_categories cat', 'c.category_id = cat.id')
                ->field([
                    'c.id',
                    'c.title',
                    'c.content',
                    'c.category_id',
                    'c.sort_order',
                    'c.status',
                    'cat.name as category_name'
                ])
                ->where('c.id', $id)
                ->find();

            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            // 添加完整的分类路径
            $content->category_path = ContentModel::getCategoryPath($content->category_id);

            return json(['code' => 200, 'data' => $content]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取数据失败：' . $e->getMessage()]);
        }
    }
}
