<?php
// 临时脚本：创建测试分类数据
require_once 'vendor/autoload.php';

use think\facade\Db;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    // 清空现有分类数据
    Db::execute('DELETE FROM km_categories');
    
    // 插入一级分类
    Db::execute("INSERT INTO `km_categories` (`id`, `parent_id`, `name`, `description`, `status`, `sort_order`, `level`, `path`, `created_at`, `updated_at`) VALUES
    (1, 0, '会员卡密', '各种会员等级的卡密', 1, 1, 1, '1', NOW(), NOW()),
    (2, 0, '基础会员', '基础功能会员卡密', 1, 2, 1, '2', NOW(), NOW())");
    
    // 插入二级分类
    Db::execute("INSERT INTO `km_categories` (`id`, `parent_id`, `name`, `description`, `status`, `sort_order`, `level`, `path`, `created_at`, `updated_at`) VALUES
    (3, 1, '三级会员', '三级会员权限', 1, 1, 2, '1,3', NOW(), NOW()),
    (4, 1, '高级会员', '高级会员权限', 1, 2, 2, '1,4', NOW(), NOW()),
    (5, 2, '月度会员', '月度基础会员', 1, 1, 2, '2,5', NOW(), NOW())");
    
    // 插入三级分类
    Db::execute("INSERT INTO `km_categories` (`id`, `parent_id`, `name`, `description`, `status`, `sort_order`, `level`, `path`, `created_at`, `updated_at`) VALUES
    (6, 3, '三级月卡', '三级会员月卡', 1, 1, 3, '1,3,6', NOW(), NOW()),
    (7, 3, '三级年卡', '三级会员年卡', 1, 2, 3, '1,3,7', NOW(), NOW()),
    (8, 4, '高级月卡', '高级会员月卡', 1, 1, 3, '1,4,8', NOW(), NOW())");
    
    echo "测试数据创建成功！\n";
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}
