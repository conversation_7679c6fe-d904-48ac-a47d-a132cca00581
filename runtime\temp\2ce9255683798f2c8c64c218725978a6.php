<?php /*a:2:{s:45:"F:\linshi\thphp\kmxt\view\settings\index.html";i:1754101270;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754043981;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-active: var(--primary-color);
            --content-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            overflow-y: auto;
            will-change: transform;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0;
            will-change: margin-left;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 内容容器 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }

        /* 响应式内边距 */
        @media (max-width: 1200px) {
            .content-wrapper {
                padding: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .content-wrapper {
                padding: 0.75rem;
            }
        }

        /* 现代化卡片样式 */
        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 特殊情况：包含下拉框的卡片需要允许内容溢出 */
        .modern-card.compact-filter {
            overflow: visible;
        }

        .modern-card.compact-filter .modern-card-body {
            overflow: visible;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .modern-card-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modern-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid var(--border-color);
        }

        /* 现代化按钮样式 */
        .modern-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius-lg);
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-outline {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .modern-btn-outline:hover {
            background: white;
            color: var(--text-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 现代化表单样式 */
        .modern-form-group {
            margin-bottom: 1.5rem;
        }

        .modern-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .modern-form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .modern-form-control::placeholder {
            color: var(--text-muted);
        }

        /* 现代化表格样式 */
        .modern-table-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            font-size: 0.875rem;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 现代化状态标签 */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .modern-badge-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .modern-badge-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .modern-badge-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .modern-badge-primary {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        /* 现代化统计卡片 */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .modern-stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .modern-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .modern-stats-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem auto;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .modern-stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .modern-stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .modern-stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .modern-stats-trend.positive {
            color: var(--success-color);
        }

        .modern-stats-trend.negative {
            color: var(--error-color);
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 32px;
    }

    .page-title-main {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .header-btn.btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .header-btn.btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .header-btn.btn-success:hover {
        background: #218838;
        border-color: #218838;
        color: white;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
    }

    .header-btn i {
        font-size: 11px;
    }

    /* 设置容器 */
    .settings-container {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 32px;
        max-width: none;
        margin: 0;
        padding: 0 20px;
    }

    /* 侧边栏 */
    .settings-sidebar {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 0;
        height: fit-content;
        position: sticky;
        top: 20px;
    }

    .sidebar-header {
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
    }

    .sidebar-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .sidebar-header h3 i {
        color: var(--primary-color);
        font-size: 18px;
    }

    .sidebar-nav {
        padding: 16px 0;
    }

    .nav-item {
        display: block;
        padding: 12px 24px;
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
        cursor: pointer;
    }

    .nav-item:hover {
        color: var(--primary-color);
        background: #f0f9ff;
        text-decoration: none;
    }

    .nav-item.active {
        color: var(--primary-color);
        background: #f0f9ff;
        border-left-color: var(--primary-color);
    }

    .nav-item i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
    }

    /* 主内容区 */
    .settings-main {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        overflow: hidden;
    }

    .main-header {
        padding: 24px 40px;
        border-bottom: 1px solid #f0f0f0;
        background: #f8f9fa;
    }

    .main-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .main-header h2 i {
        color: var(--primary-color);
        font-size: 20px;
    }

    .main-body {
        padding: 40px;
    }

    /* 设置面板 */
    .settings-panel {
        display: none;
    }

    .settings-panel.active {
        display: block;
    }

    .settings-section {
        margin-bottom: 32px;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-title i {
        color: var(--primary-color);
        font-size: 16px;
    }

    /* 表单样式 */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 32px;
        margin-bottom: 24px;
    }

    .form-group {
        margin-bottom: 24px;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    textarea.form-control {
        resize: vertical;
        min-height: 80px;
    }

    /* 开关样式 */
    .switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: var(--primary-color);
    }

    input:checked + .slider:before {
        transform: translateX(20px);
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
        text-align: center;
    }

    .btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
    }

    /* 系统信息卡片 */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }

    .info-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        text-align: center;
    }

    .info-card h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .info-card .value {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    /* 超大屏幕优化 */
    @media (min-width: 1600px) {
        .settings-container {
            grid-template-columns: 320px 1fr;
            gap: 40px;
            padding: 0 40px;
        }

        .main-body {
            padding: 48px;
        }

        .main-header {
            padding: 28px 48px;
        }

        .form-row {
            gap: 40px;
        }

        .info-grid {
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
        }
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .settings-container {
            padding: 0 16px;
        }

        .main-body {
            padding: 32px;
        }
    }

    @media (max-width: 768px) {
        .settings-container {
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 0 12px;
        }

        .settings-sidebar {
            position: static;
        }

        .sidebar-nav {
            display: flex;
            overflow-x: auto;
            padding: 8px 0;
        }

        .nav-item {
            white-space: nowrap;
            border-left: none;
            border-bottom: 3px solid transparent;
        }

        .nav-item.active {
            border-left: none;
            border-bottom-color: var(--primary-color);
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .main-body {
            padding: 24px;
        }

        .main-header {
            padding: 20px 24px;
        }
    }
</style>

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">系统设置</h1>
        <p class="text-muted mb-0">管理系统配置、性能优化和安全设置</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="clearCache()">
            <i class="fas fa-broom"></i>
            清除缓存
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="optimizeDatabase()">
            <i class="fas fa-database"></i>
            优化数据库
        </button>
    </div>
</div>

<!-- 设置容器 -->
<div class="settings-container">
    <!-- 侧边栏 -->
    <div class="settings-sidebar">
        <div class="sidebar-header">
            <h3>
                <i class="fas fa-cogs"></i>
                设置分类
            </h3>
        </div>
        <nav class="sidebar-nav">
            <a class="nav-item active" data-panel="basic">
                <i class="fas fa-info-circle"></i>
                基本设置
            </a>
            <a class="nav-item" data-panel="business">
                <i class="fas fa-business-time"></i>
                业务设置
            </a>
            <a class="nav-item" data-panel="security">
                <i class="fas fa-shield-alt"></i>
                安全设置
            </a>
            <a class="nav-item" data-panel="notification">
                <i class="fas fa-bell"></i>
                通知设置
            </a>
            <a class="nav-item" data-panel="appearance">
                <i class="fas fa-palette"></i>
                外观设置
            </a>
            <a class="nav-item" data-panel="advanced">
                <i class="fas fa-code"></i>
                高级设置
            </a>
            <a class="nav-item" data-panel="system">
                <i class="fas fa-server"></i>
                系统信息
            </a>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="settings-main">
        <div class="main-header">
            <h2 id="panelTitle">
                <i class="fas fa-info-circle"></i>
                基本设置
            </h2>
        </div>

        <div class="main-body">
            <form id="settingsForm">
                <!-- 基本设置面板 -->
                <div class="settings-panel active" id="basic-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-globe"></i>
                            网站信息
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="site_name">网站名称</label>
                                <input type="text" id="site_name" name="site_name" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['site_name']); ?>" required>
                                <div class="form-text">显示在浏览器标题栏和页面头部</div>
                            </div>
                            <div class="form-group">
                                <label for="admin_email">管理员邮箱</label>
                                <input type="email" id="admin_email" name="admin_email" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['admin_email']); ?>">
                                <div class="form-text">用于接收系统通知和重要信息</div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label for="site_description">网站描述</label>
                            <textarea id="site_description" name="site_description" class="form-control"
                                      rows="3"><?php echo htmlentities((string) $settings['site_description']); ?></textarea>
                            <div class="form-text">网站的简短描述，用于SEO优化</div>
                        </div>
                        <div class="form-group full-width">
                            <label for="site_keywords">网站关键词</label>
                            <input type="text" id="site_keywords" name="site_keywords" class="modern-form-control"
                                   value="<?php echo htmlentities((string) $settings['site_keywords']); ?>">
                            <div class="form-text">多个关键词用英文逗号分隔</div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-toggle-on"></i>
                            站点状态
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>网站状态</label>
                                <label class="switch">
                                    <input type="checkbox" name="site_status" value="1"
                                           <?php if($settings['site_status'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">关闭后网站将显示维护页面</div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label for="maintenance_message">维护提示信息</label>
                            <textarea id="maintenance_message" name="maintenance_message" class="form-control"
                                      rows="2"><?php echo htmlentities((string) $settings['maintenance_message']); ?></textarea>
                            <div class="form-text">网站维护时显示给用户的提示信息</div>
                        </div>
                    </div>
                </div>

                <!-- 业务设置面板 -->
                <div class="settings-panel" id="business-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-credit-card"></i>
                            卡密设置
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="card_expire_mode">过期时间设置</label>
                                <select id="card_expire_mode" name="card_expire_mode" class="modern-form-control" onchange="toggleExpireDays()">
                                    <option value="never" <?php if($settings['card_expire_mode'] == 'never'): ?>selected<?php endif; ?>>永不过期</option>
                                    <option value="days" <?php if($settings['card_expire_mode'] == 'days'): ?>selected<?php endif; ?>>指定天数后过期</option>
                                    <option value="custom" <?php if($settings['card_expire_mode'] == 'custom'): ?>selected<?php endif; ?>>生成时自定义</option>
                                </select>
                                <div class="form-text">设置新生成卡密的过期规则</div>
                            </div>
                            <div class="form-group" id="expire_days_group" style="display: <?php if($settings['card_expire_mode'] == 'days'): ?>block<?php else: ?>none<?php endif; ?>;">
                                <label for="card_expire_days">过期天数</label>
                                <input type="number" id="card_expire_days" name="card_expire_days" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['card_expire_days']); ?>" min="1" max="3650">
                                <div class="form-text">卡密生成后多少天过期</div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="max_generate_count">最大生成数量</label>
                                <input type="number" id="max_generate_count" name="max_generate_count" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['max_generate_count']); ?>" min="1" max="10000">
                                <div class="form-text">单次最多可生成的卡密数量</div>
                            </div>
                            <div class="form-group">
                                <label>自动删除过期卡密</label>
                                <label class="switch">
                                    <input type="checkbox" name="auto_delete_expired" value="1"
                                           <?php if($settings['auto_delete_expired'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">自动删除过期的卡密记录</div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="card_length">卡密长度</label>
                                <input type="number" id="card_length" name="card_length" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['card_length']); ?>" min="4" max="32">
                                <div class="form-text">生成卡密的字符长度（4-32位）</div>
                            </div>
                            <div class="form-group">
                                <label for="card_type">卡密类型</label>
                                <select id="card_type" name="card_type" class="modern-form-control">
                                    <option value="number" <?php if($settings['card_type'] == 'number'): ?>selected<?php endif; ?>>纯数字</option>
                                    <option value="letter" <?php if($settings['card_type'] == 'letter'): ?>selected<?php endif; ?>>纯字母</option>
                                    <option value="mixed" <?php if($settings['card_type'] == 'mixed'): ?>selected<?php endif; ?>>数字+字母</option>
                                    <option value="number_letter" <?php if($settings['card_type'] == 'number_letter'): ?>selected<?php endif; ?>>数字+字母（大小写）</option>
                                </select>
                                <div class="form-text">生成卡密的字符类型</div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="exchange_rate_limit">兑换频率限制</label>
                                <input type="number" id="exchange_rate_limit" name="exchange_rate_limit" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['exchange_rate_limit']); ?>" min="1" max="1000">
                                <div class="form-text">每小时最多兑换次数</div>
                            </div>
                            <div class="form-group">
                                <label>允许重复兑换</label>
                                <label class="switch">
                                    <input type="checkbox" name="allow_duplicate_exchange" value="1"
                                           <?php if($settings['allow_duplicate_exchange'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">是否允许同一卡密多次兑换</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置面板 -->
                <div class="settings-panel" id="security-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-key"></i>
                            API设置
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>启用API接口</label>
                                <label class="switch">
                                    <input type="checkbox" name="api_enabled" value="1"
                                           <?php if($settings['api_enabled'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">是否开放API接口供第三方调用</div>
                            </div>
                            <div class="form-group">
                                <label for="api_rate_limit">API频率限制</label>
                                <input type="number" id="api_rate_limit" name="api_rate_limit" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['api_rate_limit']); ?>" min="1" max="10000">
                                <div class="form-text">每小时最多API调用次数</div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-history"></i>
                            日志设置
                        </div>
                        <div class="form-group full-width">
                            <label for="log_retention_days">日志保留天数</label>
                            <input type="number" id="log_retention_days" name="log_retention_days" class="modern-form-control"
                                   value="<?php echo htmlentities((string) $settings['log_retention_days']); ?>" min="1" max="365" style="max-width: 200px;">
                            <div class="form-text">系统日志的保留天数，超过将自动删除</div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置面板 -->
                <div class="settings-panel" id="notification-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-envelope"></i>
                            通知方式
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>邮件通知</label>
                                <label class="switch">
                                    <input type="checkbox" name="email_notification" value="1"
                                           <?php if($settings['email_notification'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">启用邮件通知功能</div>
                            </div>
                            <div class="form-group">
                                <label>短信通知</label>
                                <label class="switch">
                                    <input type="checkbox" name="sms_notification" value="1"
                                           <?php if($settings['sms_notification'] == '1'): ?>checked<?php endif; ?>>
                                    <span class="slider"></span>
                                </label>
                                <div class="form-text">启用短信通知功能</div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-database"></i>
                            备份设置
                        </div>
                        <div class="form-group full-width">
                            <label for="backup_frequency">备份频率</label>
                            <select id="backup_frequency" name="backup_frequency" class="modern-form-control" style="max-width: 200px;">
                                <option value="daily" <?php if($settings['backup_frequency'] == 'daily'): ?>selected<?php endif; ?>>每日</option>
                                <option value="weekly" <?php if($settings['backup_frequency'] == 'weekly'): ?>selected<?php endif; ?>>每周</option>
                                <option value="monthly" <?php if($settings['backup_frequency'] == 'monthly'): ?>selected<?php endif; ?>>每月</option>
                                <option value="never" <?php if($settings['backup_frequency'] == 'never'): ?>selected<?php endif; ?>>从不</option>
                            </select>
                            <div class="form-text">自动备份数据库的频率</div>
                        </div>
                    </div>
                </div>

                <!-- 外观设置面板 -->
                <div class="settings-panel" id="appearance-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-paint-brush"></i>
                            主题设置
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="theme_color">主题色</label>
                                <input type="color" id="theme_color" name="theme_color" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['theme_color']); ?>" style="height: 40px;">
                                <div class="form-text">系统的主要颜色主题</div>
                            </div>
                            <div class="form-group">
                                <label for="logo_url">Logo地址</label>
                                <input type="url" id="logo_url" name="logo_url" class="modern-form-control"
                                       value="<?php echo htmlentities((string) $settings['logo_url']); ?>">
                                <div class="form-text">网站Logo图片的URL地址</div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label for="favicon_url">Favicon地址</label>
                            <input type="url" id="favicon_url" name="favicon_url" class="modern-form-control"
                                   value="<?php echo htmlentities((string) $settings['favicon_url']); ?>">
                            <div class="form-text">网站图标的URL地址</div>
                        </div>
                    </div>
                </div>

                <!-- 高级设置面板 -->
                <div class="settings-panel" id="advanced-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-code"></i>
                            自定义代码
                        </div>
                        <div class="form-group full-width">
                            <label for="custom_css">自定义CSS</label>
                            <textarea id="custom_css" name="custom_css" class="form-control"
                                      rows="8" style="font-family: 'Courier New', monospace;"><?php echo htmlentities((string) $settings['custom_css']); ?></textarea>
                            <div class="form-text">自定义CSS样式代码</div>
                        </div>
                        <div class="form-group full-width">
                            <label for="custom_js">自定义JavaScript</label>
                            <textarea id="custom_js" name="custom_js" class="form-control"
                                      rows="8" style="font-family: 'Courier New', monospace;"><?php echo htmlentities((string) $settings['custom_js']); ?></textarea>
                            <div class="form-text">自定义JavaScript代码</div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息面板 -->
                <div class="settings-panel" id="system-panel">
                    <div class="settings-section">
                        <div class="section-title">
                            <i class="fas fa-info-circle"></i>
                            系统信息
                        </div>
                        <div id="systemInfo" class="info-grid">
                            <!-- 系统信息将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </form>

            <!-- 保存按钮 -->
            <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #f0f0f0; text-align: right;">
                <button type="button" class="btn btn-outline" onclick="resetForm()">
                    <i class="fas fa-undo"></i>
                    重置
                </button>
                <button type="button" class="modern-btn modern-btn-primary" onclick="saveSettings()" style="margin-left: 12px;">
                    <i class="fas fa-save"></i>
                    保存设置
                </button>
            </div>
        </div>
    </div>
</div>


        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/components/form.js"></script>
<script src="/static/js/utils/validator.js"></script>
<script src="/static/js/modules/settings/navigation.js"></script>
<script src="/static/js/modules/settings/system-info.js"></script>
<script src="/static/js/modules/settings/form.js"></script>
<script src="/static/js/modules/settings/index.js"></script>
<script>


</script>

<!-- Toast 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
</style>


</body>
</html>
