<?php /*a:2:{s:41:"F:\linshi\thphp\kmxt\view\card\index.html";i:1754046871;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754043981;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-active: var(--primary-color);
            --content-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            overflow-y: auto;
            will-change: transform;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0;
            will-change: margin-left;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 内容容器 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }

        /* 响应式内边距 */
        @media (max-width: 1200px) {
            .content-wrapper {
                padding: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .content-wrapper {
                padding: 0.75rem;
            }
        }

        /* 现代化卡片样式 */
        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 特殊情况：包含下拉框的卡片需要允许内容溢出 */
        .modern-card.compact-filter {
            overflow: visible;
        }

        .modern-card.compact-filter .modern-card-body {
            overflow: visible;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .modern-card-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modern-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid var(--border-color);
        }

        /* 现代化按钮样式 */
        .modern-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius-lg);
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-outline {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .modern-btn-outline:hover {
            background: white;
            color: var(--text-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 现代化表单样式 */
        .modern-form-group {
            margin-bottom: 1.5rem;
        }

        .modern-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .modern-form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .modern-form-control::placeholder {
            color: var(--text-muted);
        }

        /* 现代化表格样式 */
        .modern-table-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            font-size: 0.875rem;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 现代化状态标签 */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .modern-badge-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .modern-badge-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .modern-badge-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .modern-badge-primary {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        /* 现代化统计卡片 */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .modern-stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .modern-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .modern-stats-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem auto;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .modern-stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .modern-stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .modern-stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .modern-stats-trend.positive {
            color: var(--success-color);
        }

        .modern-stats-trend.negative {
            color: var(--error-color);
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<style>
    .filter-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .table-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .btn-group .btn {
        margin-right: 8px;
    }

    .table {
        margin: 0;
    }

    .table th {
        background-color: var(--content-bg);
        border: none;
        font-weight: 600;
        color: var(--text-primary);
        padding: 16px 12px;
        font-size: 14px;
    }

    .table td {
        border: none;
        padding: 16px 12px;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        font-size: 14px;
    }

    .table tbody tr:hover {
        background-color: var(--content-bg);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-unused {
        background-color: #fff7e6;
        color: #d46b08;
        border: 1px solid #ffd591;
    }

    .status-used {
        background-color: #f6ffed;
        color: #389e0d;
        border: 1px solid #b7eb8f;
    }

    .status-disabled {
        background-color: #fff2f0;
        color: #cf1322;
        border: 1px solid #ffccc7;
    }
    
    .card-code {
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        background-color: var(--content-bg);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid var(--border-color);
    }

    .pagination {
        justify-content: center;
        margin-top: 24px;
    }
    
    .filter-form .form-control,
    .filter-form .form-select {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    /* 紧凑型筛选样式 */
    .compact-filter .modern-card-header {
        padding: 8px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .compact-filter .modern-card-body {
        padding: 12px 16px;
    }

    .compact-filter .modern-form-control {
        height: 36px;
        font-size: 14px;
        padding: 6px 12px;
    }

    .compact-filter .modern-btn {
        height: 36px;
        padding: 6px 16px;
        font-size: 14px;
    }

    .compact-filter .modern-card-title {
        font-size: 14px;
        font-weight: 600;
    }

    /* 分页样式 - 适配ThinkPHP默认分页结构 */
    .pagination-wrapper .pagination {
        margin: 0;
        display: flex;
        list-style: none;
        padding: 0;
        gap: 4px;
    }

    .pagination-wrapper .pagination li {
        display: inline-block;
    }

    .pagination-wrapper .pagination li a,
    .pagination-wrapper .pagination li span {
        display: inline-block;
        color: var(--text-primary);
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        padding: 8px 12px;
        font-size: 14px;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
        text-align: center;
        line-height: 1.2;
    }

    .pagination-wrapper .pagination li a:hover {
        color: var(--primary-color);
        background-color: rgba(99, 102, 241, 0.1);
        border-color: var(--primary-color);
        text-decoration: none;
    }

    .pagination-wrapper .pagination li.active span {
        color: white;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        font-weight: 600;
    }

    .pagination-wrapper .pagination li.disabled span {
        color: var(--text-muted);
        background-color: var(--card-bg);
        border-color: var(--border-color);
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 响应式分页 */
    @media (max-width: 768px) {
        .pagination-wrapper .pagination li a,
        .pagination-wrapper .pagination li span {
            padding: 6px 10px;
            font-size: 13px;
            min-width: 36px;
        }

        .pagination-wrapper {
            overflow-x: auto;
        }

        .pagination-wrapper .pagination {
            white-space: nowrap;
        }
    }
    
    @media (max-width: 768px) {
        .table-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .btn-group {
            flex-wrap: wrap;
        }
        
        .filter-form {
            flex-direction: column;
        }
        
        .filter-form .form-control,
        .filter-form .form-select {
            margin-right: 0;
            width: 100%;
        }
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 16px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .modal-header h2 i {
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 32px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 20px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    /* 生成说明样式 */
    .generate-tips {
        background: #f0f9ff;
        border-left: 4px solid var(--primary-color);
        padding: 16px;
        border-radius: 0 6px 6px 0;
        margin-bottom: 24px;
        border: 1px solid #bae7ff;
        border-left: 4px solid var(--primary-color);
    }

    .generate-tips h6 {
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
    }

    .generate-tips ul {
        margin: 0;
        padding-left: 20px;
    }

    .generate-tips li {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 4px;
        line-height: 1.5;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .modal .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .modal .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 分类树形选择器样式 */
    .category-tree-selector {
        position: relative;
        z-index: 100000;
    }

    /* 确保筛选条件卡片有足够高的层级 */
    .compact-filter {
        position: relative;
        z-index: 50000;
    }



    .tree-selector-input {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        padding: 0.75rem 1rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 48px;
    }

    .tree-selector-input:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .tree-selector-input.active {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .selected-text {
        color: var(--text-primary);
        font-size: 0.875rem;
        flex: 1;
    }

    .dropdown-arrow {
        color: var(--text-secondary);
        transition: transform 0.3s ease;
        font-size: 0.75rem;
    }

    .tree-selector-input.active .dropdown-arrow {
        transform: rotate(180deg);
    }

    .tree-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 99999;
        margin-top: 4px;
    }

    .tree-item {
        position: relative;
    }

    .tree-item-content {
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .tree-item-content:hover {
        background: rgba(99, 102, 241, 0.05);
    }

    .tree-item.selected > .tree-item-content {
        background: rgba(99, 102, 241, 0.1);
        color: var(--primary-color);
        font-weight: 500;
    }

    .expand-icon {
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s ease;
        color: var(--text-secondary);
        font-size: 0.75rem;
    }

    .expand-icon.expanded {
        transform: rotate(90deg);
    }

    /* 默认展开状态的图标 */
    .expand-icon.fa-chevron-down {
        transform: rotate(0deg);
    }

    .expand-icon.fa-chevron-down.expanded {
        transform: rotate(0deg);
    }

    .expand-icon-placeholder {
        width: 12px;
        height: 12px;
    }

    .category-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        font-size: 8px;
    }

    .level-1-icon {
        background: #e3f2fd !important;
        color: #1976d2 !important;
    }

    .level-2-icon {
        background: #e8f5e8 !important;
        color: #4caf50 !important;
    }

    .level-3-icon {
        background: #f3e5f5 !important;
        color: #9c27b0 !important;
    }

    .tree-item.level-1 > .tree-item-content {
        padding-left: 0.75rem !important;
    }

    .tree-item.level-2 > .tree-item-content {
        padding-left: 2rem !important;
    }

    .tree-item.level-3 > .tree-item-content {
        padding-left: 3rem !important;
        font-size: 0.875rem;
    }

    .tree-children {
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .tree-children.expanded {
        display: block !important;
    }

    /* 按钮样式 */
    .btn-primary, .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .btn-primary:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    /* Toast 动画 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modal-content {
            margin: 10% auto;
            width: 95%;
            max-height: 85vh;
        }

        .modal-header {
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            flex-direction: column;
        }
    }
</style>

</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">卡密管理</h1>
        <p class="text-muted mb-0">管理和监控所有卡密的状态</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button class="modern-btn modern-btn-success" onclick="exportSelected()">
            <i class="fas fa-download"></i>
            导出
        </button>
        <a href="/card/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 筛选条件 -->
<div class="modern-card mb-3 compact-filter">
    <div class="modern-card-header">
        <h6 class="modern-card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h6>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-2 align-items-center">
                <div class="col-md-2">
                    <select name="status" class="modern-form-control">
                        <option value="">全部状态</option>
                        <option value="0" <?php echo $filters['status']=='0' ? 'selected'  :  ''; ?>>未使用</option>
                        <option value="1" <?php echo $filters['status']=='1' ? 'selected'  :  ''; ?>>已使用</option>
                        <option value="2" <?php echo $filters['status']=='2' ? 'selected'  :  ''; ?>>已禁用</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <div class="category-tree-selector">
                        <div class="tree-selector-input" onclick="toggleCategoryDropdown()">
                            <span class="selected-text" id="selectedCategoryText">
                                <?php if($filters['category_id']): 
                                        function findCategoryById($categories, $id) {
                                            foreach ($categories as $category) {
                                                if ($category['id'] == $id) {
                                                    return $category;
                                                }
                                                if (!empty($category['children'])) {
                                                    $found = findCategoryById($category['children'], $id);
                                                    if ($found) return $found;
                                                }
                                            }
                                            return null;
                                        }
                                        $selectedCategory = findCategoryById($categories, $filters['category_id']);
                                        echo $selectedCategory ? $selectedCategory['name'] : '全部分类';
                                     else: ?>
                                    全部分类
                                <?php endif; ?>
                            </span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="categoryTreeDropdown" style="display: none;">
                            <div class="tree-item" data-id="" onclick="selectCategory('', '全部分类')">
                                <div class="tree-item-content">
                                    <span class="expand-icon-placeholder"></span>
                                    <i class="fas fa-list text-primary"></i>
                                    <span>全部分类</span>
                                </div>
                            </div>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <!-- 一级分类 -->
                                <div class="tree-item level-1" data-id="<?php echo htmlentities((string) $category['id']); ?>">
                                    <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $category['id']); ?>', '<?php echo htmlentities((string) $category['name']); ?>')">
                                        <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                        <div class="category-icon level-1-icon">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <span><?php echo htmlentities((string) $category['name']); ?></span>
                                    </div>
                                    <div class="tree-children" style="display: none;">
                                        <?php if(is_array($category['children']) || $category['children'] instanceof \think\Collection || $category['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $category['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$child): $mod = ($i % 2 );++$i;?>
                                            <!-- 二级分类 -->
                                            <div class="tree-item level-2" data-id="<?php echo htmlentities((string) $child['id']); ?>">
                                                <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $child['id']); ?>', '<?php echo htmlentities((string) $child['name']); ?>')">
                                                    <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                                    <div class="category-icon level-2-icon">
                                                        <i class="fas fa-square"></i>
                                                    </div>
                                                    <span><?php echo htmlentities((string) $child['name']); ?></span>
                                                </div>
                                                <div class="tree-children" style="display: none;">
                                                    <?php if(is_array($child['children']) || $child['children'] instanceof \think\Collection || $child['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $child['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$grandchild): $mod = ($i % 2 );++$i;?>
                                                        <!-- 三级分类 -->
                                                        <div class="tree-item level-3" data-id="<?php echo htmlentities((string) $grandchild['id']); ?>">
                                                            <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $grandchild['id']); ?>', '<?php echo htmlentities((string) $grandchild['name']); ?>')">
                                                                <span class="expand-icon-placeholder"></span>
                                                                <div class="category-icon level-3-icon">
                                                                    <i class="fas fa-circle"></i>
                                                                </div>
                                                                <span><?php echo htmlentities((string) $grandchild['name']); ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                        <input type="hidden" name="category_id" id="categoryIdInput" value="<?php echo htmlentities((string) $filters['category_id']); ?>">
                    </div>
                </div>

                <div class="col-md-4">
                    <input type="text" name="keyword" class="modern-form-control" placeholder="搜索卡密编号" value="<?php echo htmlentities((string) $filters['keyword']); ?>">
                </div>

                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <a href="/cards" class="modern-btn modern-btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 卡密列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            卡密列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" id="selectAllBtn" class="modern-btn modern-btn-outline btn-sm" onclick="toggleSelectAllBtn()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="showGenerateModal()">
                <i class="fas fa-plus"></i>
                生成卡密
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="batchAction('disable')">
                <i class="fas fa-ban"></i>
                批量禁用
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="showExportModal()">
                <i class="fas fa-download"></i>
                导出
            </button>
        </div>
    </div>

    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
            <thead>
                <tr>
                    <th width="50">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    </th>
                    <th>卡密编号</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>内容</th>
                    <th>创建时间</th>
                    <th>使用时间</th>
                    <th>过期时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(is_array($cards) || $cards instanceof \think\Collection || $cards instanceof \think\Paginator): $i = 0; $__LIST__ = $cards;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$card): $mod = ($i % 2 );++$i;?>
                <tr>
                    <td>
                        <input type="checkbox" class="card-checkbox" value="<?php echo htmlentities((string) $card['id']); ?>">
                    </td>
                    <td>
                        <code class="text-primary"><?php echo htmlentities((string) $card['card_code']); ?></code>
                    </td>
                    <td>
                        <span class="modern-badge modern-badge-primary"><?php echo htmlentities((string) $card['category_name']); ?></span>
                    </td>
                    <td>
                        <?php switch($card['status']): case "0": ?>
                                <span class="modern-badge modern-badge-warning">
                                    <i class="fas fa-clock"></i>
                                    未使用
                                </span>
                            <?php break; case "1": ?>
                                <span class="modern-badge modern-badge-success">
                                    <i class="fas fa-check-circle"></i>
                                    已使用
                                </span>
                            <?php break; case "2": ?>
                                <span class="modern-badge modern-badge-error">
                                    <i class="fas fa-ban"></i>
                                    已禁用
                                </span>
                            <?php break; ?>
                        <?php endswitch; ?>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlentities((string) $card['content']); ?>">
                            <?php echo htmlentities((string) $card['content']); ?>
                        </div>
                    </td>
                    <td class="text-muted"><?php echo htmlentities((string) $card['created_at']); ?></td>
                    <td class="text-muted"><?php echo !empty($card['used_at']) ? htmlentities((string) $card['used_at']) : '-'; ?></td>
                    <td class="text-muted"><?php echo !empty($card['expire_at']) ? htmlentities((string) $card['expire_at']) : '-'; ?></td>
                    <td>
                        <div class="d-flex gap-1">
                            <?php if($card['status'] != 1): ?>
                            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="updateStatus(<?php echo htmlentities((string) $card['id']); ?>, <?php echo $card['status']==0 ? 2  :  0; ?>)" title="<?php echo $card['status']==0 ? '禁用'  :  '启用'; ?>">
                                <i class="fas fa-<?php echo $card['status']==0 ? 'ban'  :  'check'; ?>"></i>
                            </button>
                            <?php endif; ?>
                            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="editCard(<?php echo htmlentities((string) $card['id']); ?>)" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; endif; else: echo "" ;endif; if(empty($cards) || (($cards instanceof \think\Collection || $cards instanceof \think\Paginator ) && $cards->isEmpty())): ?>
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                            <div>暂无卡密数据</div>
                            <div class="mt-2">
                                <button class="modern-btn modern-btn-primary" onclick="showGenerateModal()">
                                    <i class="fas fa-plus"></i>
                                    立即生成
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
        </div>
    </div>

    <!-- 分页 -->
    <div class="modern-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <div class="text-muted">
                    显示第 <?php echo htmlentities((string) $cards->currentPage()); ?> 页，共 <?php echo htmlentities((string) $cards->lastPage()); ?> 页，总计 <?php echo htmlentities((string) $cards->total()); ?> 条记录
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="text-muted small">每页显示:</span>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                        <option value="5" <?php echo $filters['limit']==5 ? 'selected'  :  ''; ?>>5条</option>
                        <option value="10" <?php echo $filters['limit']==10 ? 'selected'  :  ''; ?>>10条</option>
                        <option value="20" <?php echo $filters['limit']==20 ? 'selected'  :  ''; ?>>20条</option>
                        <option value="50" <?php echo $filters['limit']==50 ? 'selected'  :  ''; ?>>50条</option>
                        <option value="100" <?php echo $filters['limit']==100 ? 'selected'  :  ''; ?>>100条</option>
                    </select>
                </div>
            </div>
            <div class="pagination-wrapper">
                <?php if($cards->hasPages()): ?>
                    <?php echo $cards->appends(request()->param())->render(); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出卡密</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportStatus" class="form-label">状态筛选</label>
                        <select class="form-select" id="exportStatus">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                            <option value="2">已禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exportCategory" class="form-label">分类筛选</label>
                        <select class="form-select" id="exportCategory">
                            <option value="">全部分类</option>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $category['id']); ?>"><?php echo htmlentities((string) $category['name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportCards()">导出</button>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<script>
    // 全选/取消全选 - 表格头部复选框
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.card-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        updateSelectAllBtn();
    }

    // 全选/取消全选 - 按钮
    function toggleSelectAllBtn() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.card-checkbox');

        const isCurrentlyAllSelected = Array.from(checkboxes).every(cb => cb.checked);

        if (isCurrentlyAllSelected) {
            // 当前全选状态，点击后取消全选
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
            selectAllBtn.classList.remove('modern-btn-success');
            selectAllBtn.classList.add('modern-btn-outline');
        } else {
            // 当前非全选状态，点击后全选
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            selectAllCheckbox.checked = true;
            selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i> 取消全选';
            selectAllBtn.classList.remove('modern-btn-outline');
            selectAllBtn.classList.add('modern-btn-success');
        }
    }

    // 更新全选按钮状态
    function updateSelectAllBtn() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const checkboxes = document.querySelectorAll('.card-checkbox');
        const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');

        if (checkboxes.length === 0) {
            return;
        }

        if (checkedBoxes.length === checkboxes.length) {
            // 全选状态
            selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i> 取消全选';
            selectAllBtn.classList.remove('modern-btn-outline');
            selectAllBtn.classList.add('modern-btn-success');
        } else {
            // 非全选状态
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
            selectAllBtn.classList.remove('modern-btn-success');
            selectAllBtn.classList.add('modern-btn-outline');
        }
    }

    // 监听单个复选框变化
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.card-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectAllCheckbox = document.getElementById('selectAll');
                const allCheckboxes = document.querySelectorAll('.card-checkbox');
                const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');

                // 更新表格头部的全选复选框状态
                selectAllCheckbox.checked = checkedBoxes.length === allCheckboxes.length;

                // 更新全选按钮状态
                updateSelectAllBtn();
            });
        });

        // 初始化按钮状态
        updateSelectAllBtn();
    });

    // 改变每页显示数量
    function changePageSize(limit) {
        const url = new URL(window.location);
        url.searchParams.set('limit', limit);
        url.searchParams.set('page', 1); // 重置到第一页
        window.location.href = url.toString();
    }
    
    // 更新卡密状态
    function updateStatus(id, status) {
        if (!confirm('确定要修改此卡密的状态吗？')) {
            return;
        }
        
        fetch('/cards/updateStatus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id, status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('状态更新成功');
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 批量操作
    function batchAction(action) {
        const checkboxes = document.querySelectorAll('.card-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('请选择要操作的卡密');
            return;
        }

        const ids = Array.from(checkboxes).map(cb => cb.value);
        const actionText = action === 'delete' ? '删除' : '禁用';

        if (!confirm(`确定要${actionText}选中的 ${ids.length} 个卡密吗？`)) {
            return;
        }

        const url = '/cards/batchDelete';

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 显示导出模态框
    function showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }
    
    // 导出卡密
    function exportCards() {
        const password = document.getElementById('exportPassword').value;
        const status = document.getElementById('exportStatus').value;
        const categoryId = document.getElementById('exportCategory').value;
        
        if (!password) {
            alert('请输入导出密码');
            return;
        }
        
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/cards/export';
        
        const fields = {
            password: password,
            status: status,
            category_id: categoryId
        };
        
        for (const key in fields) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = fields[key];
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();
    }

    // 显示生成卡密模态框
    function showGenerateModal() {
        // 先获取分类数据
        fetch('/cards/getCategories')
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    populateGenerateCategories(data.data);
                    document.getElementById('generateModal').style.display = 'block';
                    document.body.style.overflow = 'hidden';
                } else {
                    showToast('获取分类数据失败', 'error');
                }
            })
            .catch(error => {
                showToast('获取分类数据失败', 'error');
            });
    }

    // 关闭生成卡密模态框
    function closeGenerateModal() {
        document.getElementById('generateModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        document.getElementById('generateForm').reset();
        document.getElementById('selectedCategoryText').textContent = '请选择分类';
        document.getElementById('generate_category_id').value = '';
        document.getElementById('generateCategoryDropdown').style.display = 'none';
        document.querySelector('#generateModal .tree-selector-input').classList.remove('active');
        // 重置内容选择框
        document.getElementById('generate_content_id').innerHTML = '<option value="">请先选择分类</option>';
    }

    // 填充生成模态框的分类树
    function populateGenerateCategories(categories) {
        const dropdown = document.getElementById('generateCategoryDropdown');
        dropdown.innerHTML = '';

        function renderCategoryTree(categories, level = 0, container = dropdown) {
            categories.forEach(category => {
                const item = document.createElement('div');
                item.className = `tree-item level-${level + 1}`;
                item.dataset.id = category.id;

                const content = document.createElement('div');
                content.className = 'tree-item-content';
                content.onclick = () => selectGenerateCategory(category.id, category.name);

                // 添加展开图标
                if (category.children && category.children.length > 0) {
                    const expandIcon = document.createElement('i');
                    expandIcon.className = 'fas fa-chevron-right expand-icon';
                    expandIcon.onclick = (e) => {
                        e.stopPropagation();
                        toggleGenerateTreeNode(expandIcon);
                    };
                    content.appendChild(expandIcon);
                } else {
                    const placeholder = document.createElement('span');
                    placeholder.className = 'expand-icon-placeholder';
                    content.appendChild(placeholder);
                }

                // 添加分类图标
                const iconDiv = document.createElement('div');
                iconDiv.className = `category-icon level-${level + 1}-icon`;
                const icon = document.createElement('i');
                icon.className = level === 0 ? 'fas fa-circle' : (level === 1 ? 'fas fa-square' : 'fas fa-circle');
                iconDiv.appendChild(icon);
                content.appendChild(iconDiv);

                // 添加分类名称
                const nameSpan = document.createElement('span');
                nameSpan.textContent = category.name;
                content.appendChild(nameSpan);

                item.appendChild(content);

                // 添加子分类
                if (category.children && category.children.length > 0) {
                    const childrenDiv = document.createElement('div');
                    childrenDiv.className = 'tree-children';
                    childrenDiv.style.display = 'none';
                    renderCategoryTree(category.children, level + 1, childrenDiv);
                    item.appendChild(childrenDiv);
                }

                container.appendChild(item);
            });
        }

        renderCategoryTree(categories);
    }

    // 切换生成模态框的分类下拉框
    function toggleGenerateCategoryDropdown() {
        const dropdown = document.getElementById('generateCategoryDropdown');
        const input = document.querySelector('#generateModal .tree-selector-input');

        if (dropdown.style.display === 'none' || dropdown.style.display === '') {
            dropdown.style.display = 'block';
            input.classList.add('active');
        } else {
            dropdown.style.display = 'none';
            input.classList.remove('active');
        }
    }

    // 切换生成模态框的树节点
    function toggleGenerateTreeNode(expandIcon) {
        const treeItem = expandIcon.closest('.tree-item');
        const children = treeItem.querySelector('.tree-children');

        if (children) {
            const isCurrentlyExpanded = children.style.display === 'block';

            // 手风琴效果：先关闭所有同级的展开项
            const parentContainer = treeItem.parentElement;
            const siblings = parentContainer.querySelectorAll(':scope > .tree-item');

            siblings.forEach(sibling => {
                if (sibling !== treeItem) {
                    const siblingChildren = sibling.querySelector('.tree-children');
                    const siblingIcon = sibling.querySelector('.expand-icon');

                    if (siblingChildren && siblingIcon) {
                        siblingChildren.style.display = 'none';
                        siblingIcon.classList.remove('expanded');
                        siblingIcon.classList.remove('fa-chevron-down');
                        siblingIcon.classList.add('fa-chevron-right');
                    }
                }
            });

            // 切换当前项的状态
            if (isCurrentlyExpanded) {
                children.style.display = 'none';
                expandIcon.classList.remove('expanded');
                expandIcon.classList.remove('fa-chevron-down');
                expandIcon.classList.add('fa-chevron-right');
            } else {
                children.style.display = 'block';
                expandIcon.classList.add('expanded');
                expandIcon.classList.remove('fa-chevron-right');
                expandIcon.classList.add('fa-chevron-down');
            }
        }
    }

    // 选择生成模态框的分类
    function selectGenerateCategory(categoryId, categoryName) {
        document.getElementById('generate_category_id').value = categoryId;
        document.getElementById('selectedCategoryText').textContent = categoryName;
        document.getElementById('generateCategoryDropdown').style.display = 'none';
        document.querySelector('#generateModal .tree-selector-input').classList.remove('active');

        // 加载该分类下的内容
        loadCategoryContents(categoryId);
    }

    // 加载分类下的内容
    function loadCategoryContents(categoryId) {
        const contentSelect = document.getElementById('generate_content_id');

        // 清空并显示加载状态
        contentSelect.innerHTML = '<option value="">加载中...</option>';

        fetch(`/cards/getCategoryContents?category_id=${categoryId}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    contentSelect.innerHTML = '<option value="">请选择内容</option>';
                    data.data.forEach(content => {
                        const option = document.createElement('option');
                        option.value = content.id;
                        option.textContent = content.title;
                        option.title = content.content; // 鼠标悬停显示完整内容
                        contentSelect.appendChild(option);
                    });

                    if (data.data.length === 0) {
                        contentSelect.innerHTML = '<option value="">该分类下暂无内容</option>';
                    }
                } else {
                    contentSelect.innerHTML = '<option value="">加载失败</option>';
                    showToast('加载内容失败', 'error');
                }
            })
            .catch(error => {
                contentSelect.innerHTML = '<option value="">加载失败</option>';
                showToast('加载内容失败', 'error');
            });
    }

    // 填充分类选项
    function populateCategories(categories) {
        const select = document.getElementById('generate_category_id');
        select.innerHTML = '<option value="">请选择分类</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.className = `category-option-level-${category.level}`;

            let prefix = '';
            switch(category.level) {
                case 1:
                    prefix = '■ ';
                    break;
                case 2:
                    prefix = '    ▶ ';
                    break;
                case 3:
                    prefix = '        ● ';
                    break;
            }

            option.textContent = prefix + category.name;
            select.appendChild(option);
        });
    }

    // 设置默认过期时间
    function setDefaultExpireTime() {
        const expireInput = document.getElementById('generate_expire_at');
        const now = new Date();
        now.setDate(now.getDate() + 30);

        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        expireInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 提交生成表单
    function submitGenerate() {
        const form = document.getElementById('generateForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // 验证表单
        if (!data.category_id || !data.count || !data.content_id) {
            showToast('请填写所有必填项', 'warning');
            return;
        }

        const count = parseInt(data.count);
        if (count < 1 || count > 1000) {
            showToast('生成数量必须在1-1000之间', 'warning');
            return;
        }

        // 显示加载状态
        setGenerateLoading(true);

        // 发送请求
        fetch('/cards/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showToast(result.message, 'success');
                closeGenerateModal();
                // 刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(result.message, 'error');
            }
        })
        .catch(error => {
            showToast('生成失败，请稍后重试', 'error');
        })
        .finally(() => {
            setGenerateLoading(false);
        });
    }

    // 设置生成按钮加载状态
    function setGenerateLoading(loading) {
        const btn = document.getElementById('generateSubmitBtn');
        const btnText = btn.querySelector('.btn-text');
        const loadingSpinner = btn.querySelector('.loading-spinner');

        if (loading) {
            btnText.style.display = 'none';
            loadingSpinner.style.display = 'inline-block';
            btn.disabled = true;
        } else {
            btnText.style.display = 'inline-block';
            loadingSpinner.style.display = 'none';
            btn.disabled = false;
        }
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const generateModal = document.getElementById('generateModal');
        if (event.target === generateModal) {
            closeGenerateModal();
        }
    }

    // 分类树形选择器功能
    function toggleCategoryDropdown() {
        const dropdown = document.getElementById('categoryTreeDropdown');
        const input = document.querySelector('.tree-selector-input');

        if (dropdown.style.display === 'none') {
            dropdown.style.display = 'block';
            input.classList.add('active');
        } else {
            dropdown.style.display = 'none';
            input.classList.remove('active');
        }
    }

    function selectCategory(categoryId, categoryName) {
        // 更新显示文本
        document.getElementById('selectedCategoryText').textContent = categoryName;

        // 更新隐藏输入框的值
        document.getElementById('categoryIdInput').value = categoryId;

        // 更新选中状态
        document.querySelectorAll('.tree-item').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedItem = document.querySelector(`[data-id="${categoryId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }

        // 关闭下拉框
        document.getElementById('categoryTreeDropdown').style.display = 'none';
        document.querySelector('.tree-selector-input').classList.remove('active');
    }

    function toggleTreeNode(expandIcon) {
        const treeItem = expandIcon.closest('.tree-item');
        const children = treeItem.querySelector('.tree-children');

        if (children) {
            const isCurrentlyExpanded = children.style.display === 'block';

            // 手风琴效果：先关闭所有同级的展开项
            const parentContainer = treeItem.parentElement;
            const siblings = parentContainer.querySelectorAll(':scope > .tree-item');

            siblings.forEach(sibling => {
                if (sibling !== treeItem) {
                    const siblingChildren = sibling.querySelector('.tree-children');
                    const siblingIcon = sibling.querySelector('.expand-icon');

                    if (siblingChildren && siblingIcon) {
                        siblingChildren.style.display = 'none';
                        siblingIcon.classList.remove('expanded');
                        siblingIcon.classList.remove('fa-chevron-down');
                        siblingIcon.classList.add('fa-chevron-right');
                    }
                }
            });

            // 切换当前项的状态
            if (isCurrentlyExpanded) {
                // 如果当前是展开的，则折叠
                children.style.display = 'none';
                expandIcon.classList.remove('expanded');
                expandIcon.classList.remove('fa-chevron-down');
                expandIcon.classList.add('fa-chevron-right');
            } else {
                // 如果当前是折叠的，则展开
                children.style.display = 'block';
                expandIcon.classList.add('expanded');
                expandIcon.classList.remove('fa-chevron-right');
                expandIcon.classList.add('fa-chevron-down');
            }
        }
    }

    // 点击外部关闭下拉框
    document.addEventListener('click', function(event) {
        // 处理筛选区域的分类下拉框
        const selector = document.querySelector('.category-tree-selector');
        if (selector && !selector.contains(event.target)) {
            const dropdown = document.getElementById('categoryTreeDropdown');
            if (dropdown) {
                dropdown.style.display = 'none';
                const input = document.querySelector('.tree-selector-input');
                if (input) input.classList.remove('active');
            }
        }

        // 处理生成模态框的分类下拉框
        const generateSelector = document.querySelector('#generateModal .category-tree-selector');
        if (generateSelector && !generateSelector.contains(event.target)) {
            const generateDropdown = document.getElementById('generateCategoryDropdown');
            if (generateDropdown) {
                generateDropdown.style.display = 'none';
            }
        }
    });

    // 页面加载时设置当前选中的分类
    document.addEventListener('DOMContentLoaded', function() {
        const currentCategoryId = document.getElementById('categoryIdInput').value;
        if (currentCategoryId) {
            const selectedItem = document.querySelector(`[data-id="${currentCategoryId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');

                // 展开父级节点
                let parent = selectedItem.parentElement;
                while (parent && parent.classList.contains('tree-children')) {
                    parent.style.display = 'block';
                    const parentItem = parent.parentElement;
                    const expandIcon = parentItem.querySelector('.expand-icon');
                    if (expandIcon) {
                        expandIcon.classList.add('expanded');
                    }
                    parent = parentItem.parentElement;
                }
            }
        }
    });
</script>

<!-- 生成卡密模态框 -->
<div id="generateModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2><i class="fas fa-magic"></i> 生成卡密</h2>
            <span class="close" onclick="closeGenerateModal()">&times;</span>
        </div>
        <div class="modal-body">
            <!-- 生成表单 -->
            <form id="generateForm">
                <div class="form-group">
                    <label for="generate_category_selector">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <div class="category-tree-selector">
                        <div class="tree-selector-input" onclick="toggleGenerateCategoryDropdown()">
                            <span class="selected-text" id="selectedCategoryText">请选择分类</span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="generateCategoryDropdown" style="display: none;">
                            <!-- 分类树将通过JavaScript动态加载 -->
                        </div>
                    </div>
                    <input type="hidden" id="generate_category_id" name="category_id" required>
                </div>

                <div class="form-group">
                    <label for="generate_content_id">选择内容 <span style="color: #ff4d4f;">*</span></label>
                    <select id="generate_content_id" name="content_id" class="form-control" required>
                        <option value="">请先选择分类</option>
                    </select>
                    <small class="form-text">选择该分类下的内容作为卡密兑换内容</small>
                </div>

                <div class="form-group">
                    <label for="generate_count">生成数量 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="generate_count" name="count" class="form-control"
                           min="1" max="1000" value="1" required>
                    <small class="form-text">请输入1-1000之间的数字</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeGenerateModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="generateSubmitBtn" class="btn-primary" onclick="submitGenerate()">
                <span class="btn-text">
                    <i class="fas fa-magic"></i> 开始生成
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 生成中...
                </span>
            </button>
        </div>
    </div>
</div>


</body>
</html>
