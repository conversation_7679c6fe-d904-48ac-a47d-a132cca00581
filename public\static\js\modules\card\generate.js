/**
 * 卡密生成功能模块
 * 处理卡密的生成相关功能
 */

const CardGenerate = {
    /**
     * 显示生成卡密模态框
     */
    showModal() {
        // 重置表单
        Modal.resetForm('generateForm');
        
        // 加载分类数据
        this.loadCategories();
        
        // 显示模态框
        Modal.show('generateModal');
    },
    
    /**
     * 关闭生成卡密模态框
     */
    closeModal() {
        Modal.hide('generateModal');
        Modal.resetForm('generateForm');
        
        // 清空内容选择器
        const contentSelect = document.getElementById('content_id');
        if (contentSelect) {
            contentSelect.innerHTML = '<option value="">请先选择分类</option>';
        }
    },
    
    /**
     * 加载分类数据
     */
    loadCategories() {
        const categorySelect = document.getElementById('category_id');
        if (!categorySelect) return;
        
        API.category.tree()
            .then(data => {
                if (data.code === 200) {
                    this.renderCategoryOptions(categorySelect, data.data);
                } else {
                    Toast.error('加载分类失败');
                }
            })
            .catch(error => {
                console.error('加载分类错误:', error);
                Toast.error('加载分类失败');
            });
    },
    
    /**
     * 渲染分类选项
     * @param {HTMLSelectElement} select 选择器元素
     * @param {Array} categories 分类数据
     * @param {number} level 层级
     */
    renderCategoryOptions(select, categories, level = 0) {
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = '　'.repeat(level) + category.name;
            select.appendChild(option);
            
            // 递归渲染子分类
            if (category.children && category.children.length > 0) {
                this.renderCategoryOptions(select, category.children, level + 1);
            }
        });
    },
    
    /**
     * 分类选择变化事件
     * @param {number} categoryId 分类ID
     */
    onCategoryChange(categoryId) {
        const contentSelect = document.getElementById('content_id');
        const categoryPathElement = document.getElementById('category_path');
        
        if (!categoryId) {
            if (contentSelect) {
                contentSelect.innerHTML = '<option value="">请先选择分类</option>';
            }
            if (categoryPathElement) {
                categoryPathElement.textContent = '';
            }
            return;
        }
        
        // 加载该分类下的内容
        this.loadContents(categoryId);
        
        // 更新分类路径显示
        this.updateCategoryPath(categoryId);
    },
    
    /**
     * 加载分类下的内容
     * @param {number} categoryId 分类ID
     */
    loadContents(categoryId) {
        const contentSelect = document.getElementById('content_id');
        if (!contentSelect) return;
        
        // 显示加载状态
        contentSelect.innerHTML = '<option value="">加载中...</option>';
        
        API.content.list({ category_id: categoryId, status: 1 })
            .then(data => {
                if (data.code === 200) {
                    this.renderContentOptions(contentSelect, data.data);
                } else {
                    contentSelect.innerHTML = '<option value="">加载失败</option>';
                    Toast.error('加载内容失败');
                }
            })
            .catch(error => {
                console.error('加载内容错误:', error);
                contentSelect.innerHTML = '<option value="">加载失败</option>';
                Toast.error('加载内容失败');
            });
    },
    
    /**
     * 渲染内容选项
     * @param {HTMLSelectElement} select 选择器元素
     * @param {Array} contents 内容数据
     */
    renderContentOptions(select, contents) {
        select.innerHTML = '<option value="">请选择内容</option>';
        
        if (!contents || contents.length === 0) {
            select.innerHTML = '<option value="">该分类下暂无内容</option>';
            return;
        }
        
        contents.forEach(content => {
            const option = document.createElement('option');
            option.value = content.id;
            option.textContent = content.title;
            select.appendChild(option);
        });
    },
    
    /**
     * 更新分类路径显示
     * @param {number} categoryId 分类ID
     */
    updateCategoryPath(categoryId) {
        const categoryPathElement = document.getElementById('category_path');
        if (!categoryPathElement) return;
        
        // 这里可以通过API获取分类路径，或者从已加载的分类树中查找
        // 简化处理，直接显示选中的分类名称
        const categorySelect = document.getElementById('category_id');
        if (categorySelect) {
            const selectedOption = categorySelect.options[categorySelect.selectedIndex];
            categoryPathElement.textContent = selectedOption ? selectedOption.textContent.trim() : '';
        }
    },
    
    /**
     * 提交生成表单
     */
    submit() {
        const form = document.getElementById('generateForm');
        if (!form) return;
        
        // 验证表单
        const validation = this.validateForm(form);
        if (!validation.valid) {
            Toast.error(Object.values(validation.errors)[0]);
            return;
        }
        
        // 设置加载状态
        Modal.setButtonLoading('#generateModal .modern-btn-primary', true);
        
        // 提交数据
        API.card.generate(validation.data)
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '生成成功');
                    this.closeModal();
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '生成失败');
                }
            })
            .catch(error => {
                console.error('生成卡密错误:', error);
                Toast.error('生成失败，请稍后重试');
            })
            .finally(() => {
                Modal.setButtonLoading('#generateModal .modern-btn-primary', false);
            });
    },
    
    /**
     * 验证生成表单
     * @param {HTMLFormElement} form 表单元素
     * @returns {object}
     */
    validateForm(form) {
        const data = Form.serialize(form);
        
        const rules = {
            category_id: {
                required: true,
                message: '请选择分类'
            },
            content_id: {
                required: true,
                message: '请选择内容'
            },
            count: {
                required: true,
                type: 'number',
                min: 1,
                max: 1000,
                message: '生成数量必须是1-1000之间的数字'
            }
        };
        
        return Validator.validate(data, rules);
    },
    
    /**
     * 初始化生成功能
     */
    init() {
        // 绑定分类选择变化事件
        const categorySelect = document.getElementById('category_id');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                this.onCategoryChange(e.target.value);
            });
        }
        
        // 绑定表单提交事件
        const generateForm = document.getElementById('generateForm');
        if (generateForm) {
            generateForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submit();
            });
        }
        
        // 绑定模态框关闭事件
        const closeButtons = document.querySelectorAll('#generateModal .modal-close, #generateModal .modern-btn-outline');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => this.closeModal());
        });
        
        // 绑定生成按钮事件
        const submitButton = document.querySelector('#generateModal .modern-btn-primary');
        if (submitButton) {
            submitButton.addEventListener('click', () => this.submit());
        }
    }
};

// 全局函数，供HTML调用
window.showGenerateModal = function() {
    CardGenerate.showModal();
};

window.closeGenerateModal = function() {
    CardGenerate.closeModal();
};

window.submitGenerate = function() {
    CardGenerate.submit();
};

window.onCategoryChange = function(categoryId) {
    CardGenerate.onCategoryChange(categoryId);
};

// 导出模块
window.CardGenerate = CardGenerate;
