/**
 * 卡密生成功能模块
 * 处理卡密的生成相关功能
 */

const CardGenerate = {
    /**
     * 显示生成卡密模态框
     */
    showModal() {
        // 重置表单
        Modal.resetForm('generateForm');
        
        // 加载分类数据
        this.loadCategories();
        
        // 显示模态框
        Modal.show('generateModal');
    },
    
    /**
     * 关闭生成卡密模态框
     */
    closeModal() {
        Modal.hide('generateModal');
        Modal.resetForm('generateForm');
        
        // 清空内容选择器
        const contentSelect = document.getElementById('content_id');
        if (contentSelect) {
            contentSelect.innerHTML = '<option value="">请先选择分类</option>';
        }
    },
    
    /**
     * 加载分类数据
     */
    loadCategories() {
        const categorySelect = document.getElementById('generate_category_id');
        const categoryDropdown = document.getElementById('generateCategoryDropdown');

        API.category.tree()
            .then(data => {
                if (data.code === 200) {
                    // 如果有传统的select元素，填充它
                    if (categorySelect) {
                        this.renderCategoryOptions(categorySelect, data.data);
                    }

                    // 如果有树形下拉框，填充它
                    if (categoryDropdown) {
                        this.renderCategoryTree(categoryDropdown, data.data);
                    }
                } else {
                    Toast.error('加载分类失败');
                }
            })
            .catch(error => {
                console.error('加载分类错误:', error);
                Toast.error('加载分类失败');
            });
    },
    
    /**
     * 渲染分类选项
     * @param {HTMLSelectElement} select 选择器元素
     * @param {Array} categories 分类数据
     * @param {number} level 层级
     */
    renderCategoryOptions(select, categories, level = 0) {
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = '　'.repeat(level) + category.name;
            select.appendChild(option);

            // 递归渲染子分类
            if (category.children && category.children.length > 0) {
                this.renderCategoryOptions(select, category.children, level + 1);
            }
        });
    },

    /**
     * 渲染分类树形下拉框
     * @param {HTMLElement} container 容器元素
     * @param {Array} categories 分类数据
     */
    renderCategoryTree(container, categories) {
        // 清空容器
        container.innerHTML = '';

        // 添加顶级分类选项
        const topItem = document.createElement('div');
        topItem.className = 'tree-item';
        topItem.dataset.id = '0';
        topItem.innerHTML = `
            <div class="tree-item-content">
                <span class="expand-icon-placeholder"></span>
                <i class="fas fa-home tree-item-icon"></i>
                <span class="tree-item-text">顶级分类</span>
            </div>
        `;
        topItem.addEventListener('click', () => {
            this.selectGenerateCategory(0, '顶级分类');
        });
        container.appendChild(topItem);

        // 渲染分类树
        this.renderCategoryTreeItems(container, categories, 0);
    },

    /**
     * 渲染分类树项目
     * @param {HTMLElement} container 容器元素
     * @param {Array} categories 分类数据
     * @param {number} level 层级
     */
    renderCategoryTreeItems(container, categories, level = 0) {
        categories.forEach(category => {
            const treeItem = document.createElement('div');
            treeItem.className = `tree-item tree-item-level-${level + 1}`;
            treeItem.dataset.id = category.id;

            const hasChildren = category.children && category.children.length > 0;

            treeItem.innerHTML = `
                <div class="tree-item-content">
                    ${hasChildren ?
                        '<i class="fas fa-chevron-right expand-icon"></i>' :
                        '<span class="expand-icon-placeholder"></span>'
                    }
                    <i class="fas fa-folder tree-item-icon"></i>
                    <span class="tree-item-text">${category.name}</span>
                </div>
            `;

            treeItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectGenerateCategory(category.id, category.name);
            });

            container.appendChild(treeItem);

            // 递归渲染子分类
            if (hasChildren) {
                this.renderCategoryTreeItems(container, category.children, level + 1);
            }
        });
    },

    /**
     * 选择生成分类
     * @param {number} categoryId 分类ID
     * @param {string} categoryName 分类名称
     */
    selectGenerateCategory(categoryId, categoryName) {
        // 更新隐藏字段
        const hiddenInput = document.getElementById('generate_category_id');
        if (hiddenInput) {
            hiddenInput.value = categoryId;
        }

        // 更新显示文本
        const selectedText = document.getElementById('generateSelectedCategoryText');
        if (selectedText) {
            selectedText.textContent = categoryName;
        }

        // 更新选中状态
        document.querySelectorAll('#generateCategoryDropdown .tree-item').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedItem = document.querySelector(`#generateCategoryDropdown [data-id="${categoryId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }

        // 关闭下拉框
        const dropdown = document.getElementById('generateCategoryDropdown');
        const input = document.querySelector('.tree-selector-input');
        if (dropdown) dropdown.style.display = 'none';
        if (input) input.classList.remove('active');

        // 触发分类变化事件
        this.onCategoryChange(categoryId);
    },
    
    /**
     * 分类选择变化事件
     * @param {number} categoryId 分类ID
     */
    onCategoryChange(categoryId) {
        const contentSelect = document.getElementById('generate_content_id');

        if (!categoryId) {
            if (contentSelect) {
                contentSelect.innerHTML = '<option value="">请先选择分类</option>';
            }
            return;
        }

        // 加载该分类下的内容
        this.loadContents(categoryId);
    },
    
    /**
     * 加载分类下的内容
     * @param {number} categoryId 分类ID
     */
    loadContents(categoryId) {
        const contentSelect = document.getElementById('generate_content_id');
        if (!contentSelect) return;

        // 显示加载状态
        contentSelect.innerHTML = '<option value="">加载中...</option>';

        API.content.list({ category_id: categoryId, status: 1 })
            .then(data => {
                if (data.code === 200) {
                    this.renderContentOptions(contentSelect, data.data);
                } else {
                    contentSelect.innerHTML = '<option value="">该分类下暂无内容</option>';
                    Toast.warning('该分类下暂无可用内容');
                }
            })
            .catch(error => {
                console.error('加载内容错误:', error);
                contentSelect.innerHTML = '<option value="">加载失败</option>';
                Toast.error('加载内容失败');
            });
    },
    
    /**
     * 渲染内容选项
     * @param {HTMLSelectElement} select 选择器元素
     * @param {Array} contents 内容数据
     */
    renderContentOptions(select, contents) {
        select.innerHTML = '<option value="">请选择内容</option>';
        
        if (!contents || contents.length === 0) {
            select.innerHTML = '<option value="">该分类下暂无内容</option>';
            return;
        }
        
        contents.forEach(content => {
            const option = document.createElement('option');
            option.value = content.id;
            option.textContent = content.title;
            select.appendChild(option);
        });
    },
    

    
    /**
     * 提交生成表单
     */
    submit() {
        const form = document.getElementById('generateForm');
        if (!form) return;
        
        // 验证表单
        const validation = this.validateForm(form);
        if (!validation.valid) {
            Toast.error(Object.values(validation.errors)[0]);
            return;
        }
        
        // 设置加载状态
        const submitBtn = document.getElementById('generateSubmitBtn');
        if (submitBtn) {
            const btnText = submitBtn.querySelector('.btn-text');
            const loadingSpinner = submitBtn.querySelector('.loading-spinner');
            if (btnText) btnText.style.display = 'none';
            if (loadingSpinner) loadingSpinner.style.display = 'inline-flex';
            submitBtn.disabled = true;
        }
        
        // 提交数据
        API.card.generate(validation.data)
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '生成成功');
                    this.closeModal();
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '生成失败');
                }
            })
            .catch(error => {
                console.error('生成卡密错误:', error);
                Toast.error('生成失败，请稍后重试');
            })
            .finally(() => {
                // 重置按钮状态
                const submitBtn = document.getElementById('generateSubmitBtn');
                if (submitBtn) {
                    const btnText = submitBtn.querySelector('.btn-text');
                    const loadingSpinner = submitBtn.querySelector('.loading-spinner');
                    if (btnText) btnText.style.display = 'inline-flex';
                    if (loadingSpinner) loadingSpinner.style.display = 'none';
                    submitBtn.disabled = false;
                }
            });
    },
    
    /**
     * 验证生成表单
     * @param {HTMLFormElement} form 表单元素
     * @returns {object}
     */
    validateForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        const rules = {
            category_id: {
                required: true,
                message: '请选择分类'
            },
            content_id: {
                required: true,
                message: '请选择内容'
            },
            count: {
                required: true,
                type: 'number',
                min: 1,
                max: 1000,
                message: '生成数量必须是1-1000之间的数字'
            }
        };
        
        return Validator.validate(data, rules);
    },
    
    /**
     * 初始化生成功能
     */
    init() {
        // 绑定分类选择变化事件
        const categorySelect = document.getElementById('generate_category_id');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                this.onCategoryChange(e.target.value);
            });
        }

        // 绑定表单提交事件
        const generateForm = document.getElementById('generateForm');
        if (generateForm) {
            generateForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submit();
            });
        }

        // 绑定模态框关闭事件
        const closeButtons = document.querySelectorAll('#generateModal .close, #generateModal .btn-secondary');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => this.closeModal());
        });

        // 绑定生成按钮事件
        const submitButton = document.querySelector('#generateModal .btn-primary');
        if (submitButton) {
            submitButton.addEventListener('click', () => this.submit());
        }

        // 绑定点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            const treeSelector = document.querySelector('.category-tree-selector');
            const dropdown = document.getElementById('generateCategoryDropdown');
            const input = document.querySelector('.tree-selector-input');

            if (treeSelector && !treeSelector.contains(e.target)) {
                if (dropdown) dropdown.style.display = 'none';
                if (input) input.classList.remove('active');
            }
        });

        // 绑定ESC键关闭下拉框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const dropdown = document.getElementById('generateCategoryDropdown');
                const input = document.querySelector('.tree-selector-input');
                if (dropdown && dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                    if (input) input.classList.remove('active');
                }
            }
        });
    }
};

// 全局函数，供HTML调用
window.showGenerateModal = function() {
    CardGenerate.showModal();
};

window.closeGenerateModal = function() {
    CardGenerate.closeModal();
};

window.submitGenerate = function() {
    CardGenerate.submit();
};

window.onCategoryChange = function(categoryId) {
    CardGenerate.onCategoryChange(categoryId);
};

window.toggleGenerateCategoryDropdown = function() {
    const dropdown = document.getElementById('generateCategoryDropdown');
    const input = document.querySelector('.tree-selector-input');

    if (!dropdown) return;

    if (dropdown.style.display === 'none' || dropdown.style.display === '') {
        dropdown.style.display = 'block';
        if (input) input.classList.add('active');

        // 如果下拉框为空，加载分类数据
        if (dropdown.children.length === 0) {
            CardGenerate.loadCategories();
        }
    } else {
        dropdown.style.display = 'none';
        if (input) input.classList.remove('active');
    }
};

// 导出模块
window.CardGenerate = CardGenerate;
