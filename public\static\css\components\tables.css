/**
 * 表格组件样式
 * 统一的表格样式系统
 */

/* 基础表格样式 */
.table {
  width: 100%;
  margin-bottom: var(--spacing-4);
  color: var(--gray-700);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-3) var(--spacing-4);
  vertical-align: middle;
  border-bottom: var(--border-width) solid var(--border-color);
}

.table th {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  background-color: var(--gray-100);
  border-bottom: 2px solid var(--border-color);
  text-align: left;
  font-size: var(--font-size-sm);
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

/* 表格变体 */
.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(odd):hover {
  background-color: var(--gray-100);
}

.table-bordered {
  border: var(--border-width) solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: var(--border-width) solid var(--border-color);
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

/* 表格尺寸 */
.table-sm th,
.table-sm td {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.table-lg th,
.table-lg td {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
}

/* 现代表格样式 */
.modern-table {
  width: 100%;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
}

.modern-table thead {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
}

.modern-table th {
  padding: var(--spacing-4) var(--spacing-5);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  border-bottom: var(--border-width) solid var(--border-color);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-table td {
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: var(--border-width) solid var(--gray-200);
  vertical-align: middle;
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* 表格响应式容器 */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive > .table {
  margin-bottom: 0;
}

/* 表格操作列 */
.table-actions {
  white-space: nowrap;
  width: 1%;
}

.table-actions .btn {
  margin-right: var(--spacing-1);
}

.table-actions .btn:last-child {
  margin-right: 0;
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.status-active,
.status-badge.status-enabled,
.status-badge.status-used {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-badge.status-inactive,
.status-badge.status-disabled,
.status-badge.status-unused {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-badge.status-expired,
.status-badge.status-deleted {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.status-pending {
  background-color: var(--info-light);
  color: var(--info-color);
}

/* 表格排序 */
.sortable-header {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: color var(--transition-fast);
}

.sortable-header:hover {
  color: var(--primary-color);
}

.sortable-header::after {
  content: '';
  position: absolute;
  right: var(--spacing-2);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid var(--gray-400);
  opacity: 0.5;
  transition: all var(--transition-fast);
}

.sortable-header.sort-asc::after {
  border-bottom: 4px solid var(--primary-color);
  border-top: none;
  opacity: 1;
}

.sortable-header.sort-desc::after {
  border-top: 4px solid var(--primary-color);
  border-bottom: none;
  opacity: 1;
}

/* 表格筛选 */
.table-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--gray-100);
  border-radius: var(--border-radius);
}

.table-search {
  flex: 1;
  max-width: 300px;
}

.table-filter-select {
  min-width: 150px;
}

/* 表格分页 */
.table-pagination {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: var(--spacing-4);
  border-top: var(--border-width) solid var(--border-color);
  background: var(--gray-50);
}

.table-info {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* 表格选择 */
.table-select {
  width: 1%;
  text-align: center;
}

.table-select input[type="checkbox"] {
  margin: 0;
}

/* 批量操作栏 */
.table-bulk-actions {
  display: none;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--primary-light);
  border-bottom: var(--border-width) solid var(--primary-color);
}

.table-bulk-actions.show {
  display: flex;
}

.table-bulk-info {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.table-bulk-buttons {
  display: flex;
  gap: var(--spacing-2);
  margin-left: auto;
}

/* 空状态 */
.table-empty {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  color: var(--gray-500);
}

.table-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.table-empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.table-empty-description {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

/* 加载状态 */
.table-loading {
  position: relative;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.table-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 11;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table-responsive-stack {
    display: block;
  }
  
  .table-responsive-stack thead {
    display: none;
  }
  
  .table-responsive-stack tbody,
  .table-responsive-stack tr,
  .table-responsive-stack td {
    display: block;
    width: 100%;
  }
  
  .table-responsive-stack tr {
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-4);
    background: var(--white);
  }
  
  .table-responsive-stack td {
    border: none;
    padding: var(--spacing-2) 0;
    text-align: right;
    position: relative;
    padding-left: 50%;
  }
  
  .table-responsive-stack td::before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 45%;
    text-align: left;
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
  }
}
