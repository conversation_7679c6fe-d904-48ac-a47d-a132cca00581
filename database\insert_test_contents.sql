-- 批量插入测试内容数据
-- 用于测试分页功能

-- 插入基础会员内容
INSERT INTO `km_contents` (`title`, `content`, `category_id`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('基础会员使用指南', '欢迎使用基础会员服务！\n\n功能包括：\n1. 基础资料下载\n2. 在线学习视频\n3. 社区交流权限\n4. 30天有效期\n\n使用说明：\n- 登录后即可使用所有基础功能\n- 有效期内可无限次下载资料\n- 如有问题请联系客服', 1, 100, 1, NOW(), NOW()),
('基础会员权益说明', '基础会员专享权益详细说明：\n\n✓ 免费下载基础学习资料\n✓ 观看入门级教学视频\n✓ 参与新手交流群\n✓ 获得学习进度跟踪\n✓ 享受客服优先支持\n\n注意事项：\n- 会员期间请妥善保管账号信息\n- 禁止账号共享使用\n- 违规使用将被封号处理', 1, 95, 1, NOW(), NOW()),
('基础学习资料包', '基础会员专属学习资料包内容：\n\n📚 电子书籍：\n- 入门指南 PDF\n- 基础教程合集\n- 常见问题解答\n\n🎥 视频教程：\n- 新手入门系列（10集）\n- 基础操作演示\n- 实战案例分析\n\n📝 练习题库：\n- 基础知识测试\n- 模拟练习题\n- 答案详解', 1, 90, 1, NOW(), NOW()),
('基础会员FAQ', '基础会员常见问题解答：\n\nQ: 如何激活会员？\nA: 使用卡密在系统中兑换即可自动激活。\n\nQ: 会员有效期多长？\nA: 基础会员有效期为30天。\n\nQ: 可以重复激活吗？\nA: 每个卡密只能使用一次，不可重复激活。\n\nQ: 忘记密码怎么办？\nA: 请联系客服重置密码。\n\nQ: 如何下载资料？\nA: 登录后在资料中心即可下载。', 1, 85, 1, NOW(), NOW()),
('基础会员服务条款', '基础会员服务条款：\n\n1. 服务内容\n本服务为基础会员提供学习资料和在线教程。\n\n2. 使用规则\n- 仅限个人使用，禁止商业用途\n- 不得分享账号给他人使用\n- 不得恶意下载或传播资料\n\n3. 服务期限\n基础会员服务期限为30天，到期自动失效。\n\n4. 免责声明\n本服务仅供学习参考，不承担任何法律责任。', 1, 80, 1, NOW(), NOW()),

-- 插入高级会员内容
('高级会员专享特权', '高级会员尊享特权介绍：\n\n🌟 核心特权：\n- 全部学习资料免费下载\n- 高清视频教程无限观看\n- VIP专属交流群\n- 一对一答疑服务\n- 90天超长有效期\n\n🎯 专业服务：\n- 个性化学习计划\n- 进度跟踪与提醒\n- 专业导师指导\n- 实战项目参与\n- 证书认证服务', 2, 100, 1, NOW(), NOW()),
('高级学习资源库', '高级会员专属资源库：\n\n📖 高级教材：\n- 进阶教程全集\n- 专业技能手册\n- 行业最佳实践\n- 案例分析报告\n\n🎬 高清视频：\n- 专家讲座系列\n- 实战操作演示\n- 项目开发全程\n- 技能提升训练\n\n🛠️ 实用工具：\n- 专业软件授权\n- 开发工具包\n- 模板素材库\n- 插件扩展包', 2, 95, 1, NOW(), NOW()),
('高级会员学习路径', '高级会员个性化学习路径：\n\n📈 阶段一：基础巩固（1-2周）\n- 复习基础知识点\n- 完成入门项目\n- 通过基础测试\n\n📈 阶段二：技能提升（3-6周）\n- 学习高级技巧\n- 参与实战项目\n- 导师一对一指导\n\n📈 阶段三：专业认证（7-12周）\n- 完成认证项目\n- 参加专业考试\n- 获得技能证书', 2, 90, 1, NOW(), NOW()),
('高级会员专属服务', '高级会员专属服务详情：\n\n👨‍🏫 专业导师服务：\n- 每周定期答疑\n- 学习计划制定\n- 进度跟踪指导\n- 职业规划建议\n\n🎯 实战项目机会：\n- 真实项目参与\n- 团队协作经验\n- 作品集建设\n- 行业人脉拓展\n\n🏆 认证考试支持：\n- 考试大纲解读\n- 模拟题库练习\n- 考前冲刺辅导\n- 证书申请协助', 2, 85, 1, NOW(), NOW()),
('高级会员技术支持', '高级会员技术支持服务：\n\n🔧 技术支持范围：\n- 学习平台使用问题\n- 软件安装配置\n- 代码调试协助\n- 项目部署指导\n\n⏰ 支持时间：\n- 工作日：9:00-18:00\n- 响应时间：2小时内\n- 解决时间：24小时内\n\n📞 联系方式：\n- 在线客服：优先处理\n- 技术QQ群：实时交流\n- 邮件支持：详细问题\n- 电话支持：紧急情况', 2, 80, 1, NOW(), NOW()),

-- 插入年度会员内容
('年度会员至尊体验', '年度会员至尊体验介绍：\n\n👑 至尊特权：\n- 全年365天无限制访问\n- 所有付费内容免费使用\n- 新内容第一时间获取\n- 专属客服绿色通道\n- 年度学习报告定制\n\n🎁 专属福利：\n- 生日专属礼品\n- 节日特别活动\n- 线下聚会邀请\n- 行业大会门票\n- 合作伙伴优惠', 3, 100, 1, NOW(), NOW()),
('年度学习计划', '年度会员专属学习计划：\n\n📅 第一季度（1-3月）：\n- 基础知识体系构建\n- 核心技能掌握\n- 第一个项目完成\n\n📅 第二季度（4-6月）：\n- 进阶技能学习\n- 复杂项目挑战\n- 团队协作经验\n\n📅 第三季度（7-9月）：\n- 专业认证准备\n- 实习机会推荐\n- 作品集完善\n\n📅 第四季度（10-12月）：\n- 职业规划指导\n- 就业推荐服务\n- 年度总结评估', 3, 95, 1, NOW(), NOW()),
('年度会员专属资源', '年度会员专属资源库：\n\n📚 独家内容：\n- 年度更新教程\n- 行业内幕分析\n- 专家独家分享\n- 前沿技术解读\n\n🎯 定制服务：\n- 个人学习档案\n- 定制学习路径\n- 专属项目指导\n- 职业发展规划\n\n🌐 社区特权：\n- VIP专区访问\n- 专家直播互动\n- 线下活动优先\n- 人脉圈子加入', 3, 90, 1, NOW(), NOW()),
('年度会员成长体系', '年度会员成长体系：\n\n🏆 成长等级：\n- 青铜会员（0-3月）\n- 白银会员（3-6月）\n- 黄金会员（6-9月）\n- 钻石会员（9-12月）\n\n🎖️ 成就系统：\n- 学习时长成就\n- 项目完成成就\n- 技能认证成就\n- 社区贡献成就\n\n🎁 奖励机制：\n- 等级提升奖励\n- 成就解锁奖励\n- 月度优秀奖励\n- 年度杰出奖励', 3, 85, 1, NOW(), NOW()),
('年度会员职业服务', '年度会员职业发展服务：\n\n💼 职业规划：\n- 一对一职业咨询\n- 行业趋势分析\n- 技能发展建议\n- 职业路径规划\n\n🎯 就业支持：\n- 简历优化指导\n- 面试技巧培训\n- 企业内推机会\n- 薪资谈判建议\n\n🚀 创业扶持：\n- 创业项目孵化\n- 资源对接服务\n- 投资机会介绍\n- 创业导师指导', 3, 80, 1, NOW(), NOW()),

-- 插入终身会员内容
('终身会员无限权益', '终身会员无限权益说明：\n\n♾️ 永久特权：\n- 终身免费使用所有功能\n- 永久享受最新内容更新\n- 终身技术支持服务\n- 永久社区VIP身份\n- 终身学习档案保存\n\n🌟 独家待遇：\n- 产品内测优先体验\n- 重大决策意见征询\n- 年度峰会特邀嘉宾\n- 专属纪念品定制\n- 创始人直接沟通', 4, 100, 1, NOW(), NOW()),
('终身学习伙伴', '终身会员学习伙伴计划：\n\n🤝 伙伴关系：\n- 与平台共同成长\n- 参与产品功能设计\n- 分享学习心得体会\n- 帮助新会员成长\n\n📈 持续发展：\n- 技能持续更新\n- 知识体系完善\n- 行业洞察分享\n- 职业发展跟踪\n\n🎯 价值实现：\n- 个人品牌建设\n- 专业影响力提升\n- 商业机会获取\n- 人生价值实现', 4, 95, 1, NOW(), NOW()),
('终身会员传承计划', '终身会员知识传承计划：\n\n📖 知识传承：\n- 经验分享平台\n- 导师培养计划\n- 知识库建设\n- 文化传承使命\n\n👥 社区建设：\n- 核心成员培养\n- 社区文化塑造\n- 新人引导机制\n- 活动组织策划\n\n🌱 生态发展：\n- 产业链条完善\n- 合作伙伴拓展\n- 创新项目孵化\n- 可持续发展', 4, 90, 1, NOW(), NOW()),

-- 插入企业版内容
('企业版解决方案', '企业版全方位解决方案：\n\n🏢 企业服务：\n- 多用户账号管理\n- 团队协作平台\n- 企业级安全保障\n- 定制化功能开发\n- 专属客户经理\n\n📊 管理功能：\n- 员工学习进度跟踪\n- 培训效果评估\n- 数据统计分析\n- 报告自动生成\n- 权限分级管理\n\n🎯 培训体系：\n- 企业内训定制\n- 岗位技能培训\n- 管理能力提升\n- 团队建设活动\n- 绩效考核支持', 5, 100, 1, NOW(), NOW()),
('企业版管理平台', '企业版管理平台功能：\n\n👨‍💼 管理员功能：\n- 员工账号批量管理\n- 学习内容分配\n- 进度监控统计\n- 考试成绩管理\n- 证书颁发审核\n\n📈 数据分析：\n- 学习数据可视化\n- 培训ROI分析\n- 员工能力评估\n- 部门对比报告\n- 趋势预测分析\n\n🔒 安全保障：\n- 企业级数据加密\n- 访问权限控制\n- 操作日志记录\n- 数据备份恢复\n- 合规性审计', 5, 95, 1, NOW(), NOW()),
('企业版定制服务', '企业版定制服务内容：\n\n🎨 界面定制：\n- 企业品牌元素集成\n- 个性化界面设计\n- 功能模块定制\n- 工作流程优化\n\n🔧 功能开发：\n- 专属功能模块\n- 第三方系统集成\n- API接口开发\n- 移动端应用\n\n📞 专属支持：\n- 7×24小时技术支持\n- 专属客户经理\n- 定期巡检服务\n- 紧急响应机制', 5, 90, 1, NOW(), NOW()),

-- 继续添加更多测试内容
('基础会员入门教程', '基础会员入门教程详细内容：\n\n第一章：平台介绍\n- 平台功能概述\n- 界面操作指南\n- 常用功能介绍\n\n第二章：学习方法\n- 制定学习计划\n- 高效学习技巧\n- 时间管理方法\n\n第三章：实践操作\n- 基础练习题\n- 实战小项目\n- 成果展示', 1, 75, 1, NOW(), NOW()),
('基础会员社区规则', '基础会员社区使用规则：\n\n1. 发言规范\n- 文明用语，禁止恶意攻击\n- 内容积极向上，传播正能量\n- 尊重他人观点，理性讨论\n\n2. 分享规则\n- 鼓励原创内容分享\n- 标注转载来源\n- 禁止广告和垃圾信息\n\n3. 互助精神\n- 积极帮助新手\n- 分享学习经验\n- 共同进步成长', 1, 70, 1, NOW(), NOW()),
('基础会员学习工具', '基础会员专用学习工具：\n\n📝 笔记工具：\n- 在线笔记编辑器\n- 知识点标记功能\n- 笔记分类管理\n- 搜索和导出功能\n\n⏰ 时间管理：\n- 学习计划制定\n- 进度跟踪提醒\n- 番茄工作法计时器\n- 学习统计报告\n\n🎯 练习系统：\n- 章节练习题\n- 模拟考试\n- 错题本功能\n- 成绩分析', 1, 65, 1, NOW(), NOW()),
('高级会员进阶指南', '高级会员进阶学习指南：\n\n🚀 技能提升路径：\n阶段1：基础巩固（第1-2周）\n- 复习基础知识\n- 完成入门项目\n- 通过基础测试\n\n阶段2：技能进阶（第3-8周）\n- 学习高级技巧\n- 参与实战项目\n- 获得导师指导\n\n阶段3：专业认证（第9-12周）\n- 准备认证考试\n- 完成毕业项目\n- 获得技能证书', 2, 75, 1, NOW(), NOW()),
('高级会员项目实战', '高级会员项目实战内容：\n\n🎯 项目类型：\n- Web开发项目\n- 移动应用开发\n- 数据分析项目\n- 人工智能应用\n- 区块链项目\n\n📋 项目流程：\n1. 需求分析\n2. 技术选型\n3. 架构设计\n4. 编码实现\n5. 测试部署\n6. 项目总结\n\n🏆 项目收获：\n- 实战经验积累\n- 作品集建设\n- 团队协作能力\n- 问题解决能力', 2, 70, 1, NOW(), NOW()),
('高级会员认证考试', '高级会员认证考试说明：\n\n📋 考试内容：\n- 理论知识测试（40%）\n- 实践操作考核（40%）\n- 项目答辩环节（20%）\n\n⏰ 考试安排：\n- 考试时间：每月最后一周\n- 考试时长：3小时\n- 补考机会：2次\n\n🏅 证书获得：\n- 总分80分以上\n- 各部分不低于70分\n- 项目答辩通过\n- 颁发官方认证证书', 2, 65, 1, NOW(), NOW()),
('年度会员专属活动', '年度会员专属活动安排：\n\n🎉 季度活动：\n春季：技能竞赛大会\n夏季：创新项目展示\n秋季：行业交流峰会\n冬季：年度总结庆典\n\n🎯 月度活动：\n- 专家讲座分享\n- 会员交流聚会\n- 技术沙龙讨论\n- 项目路演展示\n\n🏆 特殊活动：\n- 生日会员专场\n- 节日主题活动\n- 里程碑庆祝\n- 成就表彰大会', 3, 75, 1, NOW(), NOW()),
('年度会员导师计划', '年度会员导师计划详情：\n\n👨‍🏫 导师团队：\n- 行业资深专家\n- 知名企业技术总监\n- 创业成功人士\n- 学术界权威教授\n\n🎯 指导内容：\n- 个人发展规划\n- 技能提升建议\n- 职业路径指导\n- 项目实战辅导\n\n📅 指导安排：\n- 每月一对一面谈\n- 随时在线答疑\n- 项目全程跟踪\n- 定期进度评估', 3, 70, 1, NOW(), NOW()),
('年度会员行业报告', '年度会员专属行业报告：\n\n📊 市场分析：\n- 行业发展趋势\n- 技术演进方向\n- 人才需求变化\n- 薪资水平统计\n\n🔍 深度洞察：\n- 新兴技术解读\n- 商业模式分析\n- 投资热点追踪\n- 政策影响评估\n\n📈 预测展望：\n- 未来发展预测\n- 机遇挑战分析\n- 投资建议指导\n- 职业规划建议', 3, 65, 1, NOW(), NOW()),
('终身会员传奇故事', '终身会员成功故事分享：\n\n🌟 张三的创业之路：\n从零基础学员到独角兽公司CTO\n- 学习历程：3年持续学习\n- 关键转折：参与开源项目\n- 成功秘诀：永不放弃的精神\n- 现状：年薪百万，团队200人\n\n🚀 李四的转型传奇：\n从传统行业到AI专家\n- 背景：传统制造业工程师\n- 转型：35岁开始学习AI\n- 突破：发表多篇顶级论文\n- 成就：AI独角兽公司首席科学家', 4, 85, 1, NOW(), NOW()),
('终身会员智慧分享', '终身会员智慧经验分享：\n\n💡 学习心得：\n"学习不是一蹴而就的过程，需要持续的投入和坚持。最重要的是保持好奇心和学习的热情。"\n\n🎯 职业建议：\n"选择比努力更重要，但努力比选择更可控。在正确的方向上持续努力，成功只是时间问题。"\n\n🌱 人生感悟：\n"技术会过时，但学习能力不会。培养终身学习的习惯，是应对未来不确定性的最好方式。"', 4, 80, 1, NOW(), NOW()),
('企业版成功案例', '企业版客户成功案例：\n\n🏢 某知名互联网公司：\n- 员工规模：5000+\n- 使用场景：新员工培训\n- 实施效果：培训效率提升300%\n- 成本节约：年节约培训成本500万\n\n🏭 某制造业集团：\n- 员工规模：10000+\n- 使用场景：技能转型培训\n- 实施效果：90%员工完成转型\n- 业务提升：数字化转型成功\n\n🏦 某金融机构：\n- 员工规模：3000+\n- 使用场景：合规培训\n- 实施效果：100%通过监管检查\n- 风险控制：零合规事故', 5, 85, 1, NOW(), NOW()),
('企业版培训体系', '企业版完整培训体系：\n\n📚 课程体系：\n- 新员工入职培训\n- 在职技能提升\n- 管理能力培养\n- 领导力发展\n- 专业认证培训\n\n🎯 培训方式：\n- 在线自主学习\n- 直播互动教学\n- 线下集中培训\n- 项目实战演练\n- 导师一对一指导\n\n📊 效果评估：\n- 学习进度跟踪\n- 知识掌握测试\n- 技能应用评估\n- 绩效改善分析\n- ROI投资回报', 5, 80, 1, NOW(), NOW()),
('企业版技术架构', '企业版技术架构说明：\n\n🏗️ 系统架构：\n- 微服务架构设计\n- 容器化部署\n- 负载均衡集群\n- 数据库读写分离\n- 缓存优化策略\n\n🔒 安全保障：\n- 多层安全防护\n- 数据加密传输\n- 访问权限控制\n- 操作审计日志\n- 灾备恢复机制\n\n📈 性能优化：\n- 支持万级并发\n- 毫秒级响应时间\n- 99.9%可用性\n- 弹性扩容能力\n- 智能监控告警', 5, 75, 1, NOW(), NOW());
