<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Cache;

/**
 * 系统设置控制器
 */
class Settings extends BaseController
{
    /**
     * 系统设置首页
     */
    public function index()
    {
        if ($this->request->isPost()) {
            return $this->save();
        }

        // 获取系统设置
        $settings = $this->getSettings();

        return view('settings/index', [
            'settings' => $settings
        ]);
    }

    /**
     * 保存系统设置
     */
    private function save()
    {
        $data = $this->request->post();
        
        try {
            // 验证数据
            $validate = $this->validate($data, [
                'site_name' => 'require|max:100',
                'site_description' => 'max:500',
                'admin_email' => 'email|max:100',
                'card_expire_days' => 'integer|between:1,3650',
                'max_generate_count' => 'integer|between:1,10000',
                'exchange_rate_limit' => 'integer|between:1,1000',
                'card_length' => 'integer|between:4,32',
                'card_type' => 'in:number,letter,mixed,number_letter',
                'site_status' => 'in:1,0'
            ]);

            if ($validate !== true) {
                return json(['code' => 400, 'message' => $validate]);
            }

            // 保存设置
            foreach ($data as $key => $value) {
                $this->setSetting($key, $value);
            }

            // 清除缓存
            Cache::clear();

            return json(['code' => 200, 'message' => '设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取所有设置
     */
    private function getSettings()
    {
        // 默认设置
        $defaults = [
            'site_name' => '卡密兑换管理系统',
            'site_description' => '专业的卡密兑换管理平台',
            'site_keywords' => '卡密,兑换,管理系统',
            'admin_email' => '<EMAIL>',
            'site_status' => '1',
            'maintenance_message' => '系统维护中，请稍后访问',
            'card_expire_days' => '30',
            'max_generate_count' => '1000',
            'exchange_rate_limit' => '10',
            'card_length' => '8',
            'card_type' => 'mixed',
            'allow_duplicate_exchange' => '0',
            'auto_delete_expired' => '0',
            'email_notification' => '1',
            'sms_notification' => '0',
            'log_retention_days' => '30',
            'backup_frequency' => 'daily',
            'theme_color' => '#1890ff',
            'logo_url' => '',
            'favicon_url' => '',
            'custom_css' => '',
            'custom_js' => '',
            'api_enabled' => '1',
            'api_rate_limit' => '100'
        ];

        $settings = [];
        foreach ($defaults as $key => $default) {
            $settings[$key] = $this->getSetting($key, $default);
        }

        return $settings;
    }

    /**
     * 获取单个设置
     */
    private function getSetting($key, $default = '')
    {
        try {
            $setting = Db::name('settings')->where('key', $key)->find();
            return $setting ? $setting['value'] : $default;
        } catch (\Exception $e) {
            // 如果表不存在，先创建表
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                $this->createSettingsTable();
                // 重新尝试获取设置
                try {
                    $setting = Db::name('settings')->where('key', $key)->find();
                    return $setting ? $setting['value'] : $default;
                } catch (\Exception $e2) {
                    return $default;
                }
            }
            return $default;
        }
    }

    /**
     * 设置单个配置
     */
    private function setSetting($key, $value)
    {
        $exists = Db::name('settings')->where('key', $key)->find();
        
        if ($exists) {
            Db::name('settings')->where('key', $key)->update([
                'value' => $value,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            Db::name('settings')->insert([
                'key' => $key,
                'value' => $value,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 系统信息
     */
    public function systemInfo()
    {
        $info = [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'mysql_version' => $this->getMysqlVersion(),
            'disk_usage' => $this->getDiskUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'system_load' => $this->getSystemLoad(),
            'uptime' => $this->getUptime()
        ];

        return json(['code' => 200, 'data' => $info]);
    }

    /**
     * 获取MySQL版本
     */
    private function getMysqlVersion()
    {
        try {
            $result = Db::query('SELECT VERSION() as version');
            return $result[0]['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage()
    {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'percent' => round(($used / $total) * 100, 2)
        ];
    }

    /**
     * 获取内存使用情况
     */
    private function getMemoryUsage()
    {
        $memory = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        $limit = ini_get('memory_limit');
        
        return [
            'current' => $this->formatBytes($memory),
            'peak' => $this->formatBytes($peak),
            'limit' => $limit
        ];
    }

    /**
     * 获取系统负载
     */
    private function getSystemLoad()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2)
            ];
        }
        return null;
    }

    /**
     * 获取系统运行时间
     */
    private function getUptime()
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $uptime = file_get_contents('/proc/uptime');
            if ($uptime) {
                $seconds = (int)explode(' ', $uptime)[0];
                return $this->formatUptime($seconds);
            }
        }
        return 'Unknown';
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 格式化运行时间
     */
    private function formatUptime($seconds)
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        return "{$days}天 {$hours}小时 {$minutes}分钟";
    }

    /**
     * 清除缓存
     */
    public function clearCache()
    {
        try {
            Cache::clear();
            return json(['code' => 200, 'message' => '缓存清除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '缓存清除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 数据库优化
     */
    public function optimizeDatabase()
    {
        try {
            // 获取所有表
            $tables = Db::query('SHOW TABLES');
            $optimized = 0;
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                Db::execute("OPTIMIZE TABLE `{$tableName}`");
                $optimized++;
            }
            
            return json(['code' => 200, 'message' => "成功优化 {$optimized} 个数据表"]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '数据库优化失败：' . $e->getMessage()]);
        }
    }

    /**
     * 创建settings表
     */
    private function createSettingsTable()
    {
        try {
            // 创建settings表
            $sql = "
            CREATE TABLE IF NOT EXISTS `km_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID',
              `key` varchar(100) NOT NULL COMMENT '设置键名',
              `value` text COMMENT '设置值',
              `description` varchar(255) DEFAULT NULL COMMENT '设置描述',
              `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk_key` (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';
            ";

            Db::execute($sql);

            // 插入默认设置
            $settings = [
                ['key' => 'site_name', 'value' => '卡密兑换管理系统', 'description' => '网站名称'],
                ['key' => 'site_description', 'value' => '专业的卡密兑换管理平台', 'description' => '网站描述'],
                ['key' => 'site_keywords', 'value' => '卡密,兑换,管理系统', 'description' => '网站关键词'],
                ['key' => 'admin_email', 'value' => '<EMAIL>', 'description' => '管理员邮箱'],
                ['key' => 'site_status', 'value' => '1', 'description' => '网站状态：1-正常，0-维护'],
                ['key' => 'maintenance_message', 'value' => '系统维护中，请稍后访问', 'description' => '维护提示信息'],
                ['key' => 'card_expire_days', 'value' => '30', 'description' => '卡密默认过期天数'],
                ['key' => 'max_generate_count', 'value' => '1000', 'description' => '最大生成数量'],
                ['key' => 'exchange_rate_limit', 'value' => '10', 'description' => '兑换频率限制'],
                ['key' => 'card_length', 'value' => '8', 'description' => '卡密长度'],
                ['key' => 'card_type', 'value' => 'mixed', 'description' => '卡密类型'],
                ['key' => 'allow_duplicate_exchange', 'value' => '0', 'description' => '允许重复兑换'],
                ['key' => 'auto_delete_expired', 'value' => '0', 'description' => '自动删除过期卡密'],
                ['key' => 'email_notification', 'value' => '1', 'description' => '邮件通知'],
                ['key' => 'sms_notification', 'value' => '0', 'description' => '短信通知'],
                ['key' => 'log_retention_days', 'value' => '30', 'description' => '日志保留天数'],
                ['key' => 'backup_frequency', 'value' => 'daily', 'description' => '备份频率'],
                ['key' => 'theme_color', 'value' => '#1890ff', 'description' => '主题色'],
                ['key' => 'logo_url', 'value' => '', 'description' => 'Logo地址'],
                ['key' => 'favicon_url', 'value' => '', 'description' => 'Favicon地址'],
                ['key' => 'custom_css', 'value' => '', 'description' => '自定义CSS'],
                ['key' => 'custom_js', 'value' => '', 'description' => '自定义JavaScript'],
                ['key' => 'api_enabled', 'value' => '1', 'description' => '启用API接口'],
                ['key' => 'api_rate_limit', 'value' => '100', 'description' => 'API频率限制']
            ];

            foreach ($settings as $setting) {
                // 检查是否已存在
                $exists = Db::name('settings')->where('key', $setting['key'])->find();
                if (!$exists) {
                    Db::name('settings')->insert($setting);
                }
            }

        } catch (\Exception $e) {
            // 忽略创建表的错误
        }
    }
}
