<?php /*a:2:{s:45:"F:\linshi\thphp\kmxt\view\category\index.html";i:1754100825;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754043981;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-active: var(--primary-color);
            --content-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            overflow-y: auto;
            will-change: transform;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0;
            will-change: margin-left;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 内容容器 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }

        /* 响应式内边距 */
        @media (max-width: 1200px) {
            .content-wrapper {
                padding: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .content-wrapper {
                padding: 0.75rem;
            }
        }

        /* 现代化卡片样式 */
        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 特殊情况：包含下拉框的卡片需要允许内容溢出 */
        .modern-card.compact-filter {
            overflow: visible;
        }

        .modern-card.compact-filter .modern-card-body {
            overflow: visible;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .modern-card-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modern-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid var(--border-color);
        }

        /* 现代化按钮样式 */
        .modern-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius-lg);
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-outline {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .modern-btn-outline:hover {
            background: white;
            color: var(--text-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 现代化表单样式 */
        .modern-form-group {
            margin-bottom: 1.5rem;
        }

        .modern-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .modern-form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .modern-form-control::placeholder {
            color: var(--text-muted);
        }

        /* 现代化表格样式 */
        .modern-table-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            font-size: 0.875rem;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 现代化状态标签 */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .modern-badge-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .modern-badge-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .modern-badge-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .modern-badge-primary {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        /* 现代化统计卡片 */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .modern-stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .modern-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .modern-stats-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem auto;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .modern-stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .modern-stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .modern-stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .modern-stats-trend.positive {
            color: var(--success-color);
        }

        .modern-stats-trend.negative {
            color: var(--error-color);
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<style>
    .page-header {
        margin-bottom: 32px;
    }

    .page-title-main {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .header-btn.btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .header-btn.btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .header-btn.btn-success:hover {
        background: #218838;
        border-color: #218838;
        color: white;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
    }

    .header-btn.btn-warning {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .header-btn.btn-warning:hover {
        background: #5a6268;
        border-color: #5a6268;
        color: white;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25);
    }

    .header-btn i {
        font-size: 11px;
    }

    /* 统计卡片 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 110px;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--accent-color);
        border-radius: 16px 16px 0 0;
    }

    .stat-card.blue::before {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
    }

    .stat-card.green::before {
        background: linear-gradient(135deg, #52c41a, #73d13d);
    }

    .stat-card.purple::before {
        background: linear-gradient(135deg, #722ed1, #9254de);
    }

    .stat-card.orange::before {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
    }

    .stat-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .stat-icon.blue {
        background: linear-gradient(135deg, #e6f7ff, #bae7ff);
        color: #1890ff;
    }

    .stat-icon.green {
        background: linear-gradient(135deg, #f6ffed, #d9f7be);
        color: #52c41a;
    }

    .stat-icon.purple {
        background: linear-gradient(135deg, #f9f0ff, #efdbff);
        color: #722ed1;
    }

    .stat-icon.orange {
        background: linear-gradient(135deg, #fff7e6, #ffd591);
        color: #fa8c16;
    }

    .stat-label {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #262626;
        line-height: 1;
        margin: 0;
    }

    /* 分类列表卡片 */
    .category-list-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .category-list-header {
        padding: 24px 24px 0 24px;
        border-bottom: none;
    }

    .category-list-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .category-list-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0 0 24px 0;
    }

    /* 分类表格样式 */
    .category-table {
        width: 100%;
        border-collapse: collapse;
    }

    .category-table th {
        background: #fafafa;
        padding: 16px 20px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 2px solid #f0f0f0;
        font-size: 14px;
    }

    .category-table td {
        padding: 16px 20px;
        border-bottom: 1px solid #f5f5f5;
        vertical-align: middle;
        font-size: 14px;
    }

    .category-row {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .category-row:hover {
        background: #fafafa;
    }

    .category-row.selected {
        background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        position: relative;
    }



    .category-row.level-1 {
        background: #f9f9f9;
    }

    .category-row.level-2 {
        background: #fdfdfd;
    }

    .category-row.level-3 {
        background: white;
    }

    .category-row.level-1:hover:not(.selected) {
        background: #f0f0f0;
    }

    .category-row.level-2:hover:not(.selected) {
        background: #f8f8f8;
    }

    .category-row.level-3:hover:not(.selected) {
        background: #fafafa;
    }

    /* 选中状态覆盖所有层级的背景色 */
    .category-row.selected.level-1,
    .category-row.selected.level-2,
    .category-row.selected.level-3 {
        background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%) !important;
    }

    /* 分类名称列 */
    .category-name-cell {
        display: flex;
        align-items: center;
    }

    /* 选中状态的小蓝点 */
    .category-row.selected .category-name-text::after {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        background: var(--primary-color);
        border-radius: 50%;
        margin-left: 8px;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        animation: selectedPulse 2s infinite;
        vertical-align: middle;
    }

    @keyframes selectedPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.2);
        }
    }

    .expand-arrow {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        color: #8c8c8c;
        transition: transform 0.2s ease;
        font-size: 10px;
        cursor: pointer;
        border-radius: 2px;
    }

    .expand-arrow:hover {
        background: rgba(0,0,0,0.04);
    }

    .expand-arrow.expanded {
        transform: rotate(90deg);
    }

    .expand-arrow.no-children {
        visibility: hidden;
    }

    .category-icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 12px;
        flex-shrink: 0;
    }

    .category-icon.level-1 {
        background: #e6f7ff;
        color: #1890ff;
    }

    .category-icon.level-2 {
        background: #f6ffed;
        color: #52c41a;
    }

    .category-icon.level-3 {
        background: #f9f0ff;
        color: #722ed1;
    }

    .category-name {
        font-weight: 500;
        color: var(--text-primary);
    }

    .category-row.level-1 .category-name {
        font-weight: 600;
    }

    .category-row.level-2 .category-name-cell {
        padding-left: 20px;
    }

    .category-row.level-3 .category-name-cell {
        padding-left: 40px;
    }

    /* 状态标签 */
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-enabled {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #d9f7be;
    }

    .status-disabled {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
    }

    /* 操作按钮 */
    .category-actions {
        display: flex;
        align-items: center;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .category-row:hover .category-actions {
        opacity: 1;
    }

    .category-row.selected .category-actions {
        opacity: 1;
    }

    .action-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        transition: all 0.2s ease;
        position: relative;
        z-index: 10;
    }

    .action-btn:hover {
        background: var(--primary-color);
        color: white;
    }

    .action-btn.edit:hover {
        background: #52c41a;
    }

    .action-btn.delete:hover {
        background: #ff4d4f;
    }

    .action-btn.toggle:hover {
        background: #fa8c16;
    }

    /* 子分类默认隐藏 */
    .category-child {
        display: none;
    }

    .badge {
        font-size: 11px;
        padding: 4px 8px;
    }

    /* ID列样式 */
    .category-id {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
        color: var(--text-secondary);
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
        min-width: 30px;
        text-align: center;
    }





    /* 排序控制样式 */
    .sort-control {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sort-input {
        width: 60px;
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        text-align: center;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .sort-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        outline: none;
    }

    .sort-input:hover {
        border-color: var(--primary-color);
    }

    .sort-input.changed {
        background-color: #fff7e6;
        border-color: #ffa940;
    }

    /* 分类列表头部样式 */
    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
    }

    .header-left {
        flex: 1;
    }

    .header-right {
        flex-shrink: 0;
    }

    .help-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        color: #495057;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .help-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #212529;
        transform: translateY(-1px);
    }

    .help-btn i {
        font-size: 16px;
        color: var(--primary-color);
    }

    /* 自定义模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 0;
        border-radius: 16px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
        z-index: 10001;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .modal-header h2 i {
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 32px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 20px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
    }

    .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-right: 12px;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    .btn-primary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* 树形选择器样式 */
    .category-tree-selector {
        position: relative;
        width: 100%;
    }

    .tree-selector-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .tree-selector-header:hover {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .tree-selector-header.active {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }

    .tree-selector-header .selected-text {
        flex: 1;
        color: #333;
        font-size: 14px;
    }

    .tree-selector-header i {
        color: #999;
        transition: transform 0.2s ease;
    }

    .tree-selector-header.active i {
        transform: rotate(180deg);
    }

    .tree-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 6px 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
    }

    .tree-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f5f5f5;
    }

    .tree-item:last-child {
        border-bottom: none;
    }

    .tree-item:hover {
        background-color: #f8f9ff;
    }

    .tree-item.selected {
        background-color: #e6f0ff;
        color: #667eea;
    }

    .tree-item i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
        font-size: 12px;
    }

    .tree-item .expand-icon {
        margin-right: 4px;
        width: 12px;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .tree-item .expand-icon.expanded {
        transform: rotate(90deg);
    }

    .tree-item.level-1 {
        padding-left: 12px;
    }

    .tree-item.level-2 {
        padding-left: 32px;
    }

    .tree-item.level-3 {
        padding-left: 52px;
    }

    .tree-item.collapsed {
        display: none;
    }

    .tree-item.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f8f8;
    }

    .tree-item.disabled:hover {
        background-color: #f8f8f8;
    }

    .tree-item.disabled span {
        color: #999;
        font-style: italic;
    }

    .help-section {
        margin-bottom: 32px;
    }

    .help-section:last-child {
        margin-bottom: 0;
    }

    .help-section h3 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .help-section h3 i {
        color: var(--primary-color);
        font-size: 20px;
    }

    .hierarchy-levels {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .level-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .level-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 28px;
        border-radius: 14px;
        font-size: 12px;
        font-weight: 700;
        color: white;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .level-badge.level-1 {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
    }

    .level-badge.level-2 {
        background: linear-gradient(135deg, #52c41a, #73d13d);
    }

    .level-badge.level-3 {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
    }

    .level-content {
        flex: 1;
    }

    .level-desc {
        display: block;
        font-size: 15px;
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 6px;
    }

    .level-example {
        display: block;
        font-size: 13px;
        color: #6c757d;
        font-style: italic;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .info-item i {
        font-size: 20px;
        margin-top: 2px;
        flex-shrink: 0;
    }

    .info-item strong {
        display: block;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .info-item p {
        margin: 0;
        font-size: 13px;
        color: #6c757d;
        line-height: 1.4;
    }



        .header-content {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
        }

        .help-btn {
            align-self: flex-start;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .level-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
    }

    @keyframes flash-success {
        0% { background-color: inherit; }
        50% { background-color: #f6ffed; border-left: 4px solid #52c41a; }
        100% { background-color: inherit; }
    }



    .category-row.flash-success {
        animation: flash-success 1s ease;
    }

    /* 分类名称可点击样式 */
    .category-name-clickable {
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        flex: 1;
        padding: 4px 0;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    .category-name-clickable:hover {
        background: rgba(24, 144, 255, 0.08);
    }

    .category-name-text {
        margin-left: 4px;
    }

    /* 动画效果 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 拖拽时的视觉效果 */
    .category-row.dragging {
        opacity: 0.5;
    }

    .category-row.drag-over::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--primary-color);
        z-index: 10;
    }

    /* 子分类容器 */
    .category-children {
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .category-children.collapsed {
        max-height: 0;
        opacity: 0;
    }

    .category-children.expanded {
        max-height: 2000px;
        opacity: 1;
    }

    /* 状态指示器 */
    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .status-dot.enabled {
        background: #52c41a;
    }

    .status-dot.disabled {
        background: #ff4d4f;
    }

    /* 操作按钮 */
    .tree-actions {
        display: none;
        margin-left: 8px;
        gap: 4px;
    }

    .tree-item:hover .tree-actions {
        display: flex;
    }

    .tree-item.selected .tree-actions {
        display: flex;
    }

    .action-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        color: inherit;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .tree-item:not(.selected) .action-btn {
        background: rgba(0, 0, 0, 0.05);
        color: #8c8c8c;
    }

    .tree-item:not(.selected) .action-btn:hover {
        background: rgba(0, 0, 0, 0.1);
        color: var(--primary-color);
    }

    /* 状态指示器 */
    .status-indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .status-indicator.enabled {
        background: #52c41a;
    }

    .status-indicator.disabled {
        background: #ff4d4f;
    }

    /* 空状态 */
    .empty-tree {
        text-align: center;
        padding: 60px 20px;
        color: #8c8c8c;
    }

    .empty-tree i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #d9d9d9;
    }

    /* 详情面板 */
    .category-details {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .details-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .details-content {
        display: none;
    }

    .details-content.show {
        display: block;
    }

    .detail-item {
        display: flex;
        margin-bottom: 12px;
    }

    .detail-label {
        width: 80px;
        color: var(--text-secondary);
        font-size: 14px;
        flex-shrink: 0;
    }

    .detail-value {
        flex: 1;
        color: var(--text-primary);
        font-size: 14px;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-enabled {
        background-color: #f6ffed;
        color: #389e0d;
        border: 1px solid #b7eb8f;
    }

    .status-disabled {
        background-color: #fff2f0;
        color: #cf1322;
        border: 1px solid #ffccc7;
    }

    @media (max-width: 768px) {
        .category-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .btn-group {
            flex-wrap: wrap;
        }

        .tree-item.level-2 {
            padding-left: 28px;
        }

        .tree-item.level-3 {
            padding-left: 44px;
        }
    }
</style>

</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">分类管理</h1>
        <p class="text-muted mb-0">管理和配置分类层级结构</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="expandAll()">
            <i class="fas fa-expand-arrows-alt"></i>
            全部展开
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="collapseAll()">
            <i class="fas fa-compress-arrows-alt"></i>
            全部收起
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="importCategories()">
            <i class="fas fa-file-import"></i>
            导入分类
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="exportCategories()">
            <i class="fas fa-file-export"></i>
            导出分类
        </button>
        <button class="modern-btn modern-btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus"></i>
            新建分类
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Category::where('parent_id', 0)->where('status', 1)->count();
             ?>
        </div>
        <div class="modern-stats-label">顶级分类</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-sitemap"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Category::where('status', 1)->count();
             ?>
        </div>
        <div class="modern-stats-label">分类总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="fas fa-tags"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Card::count();
             ?>
        </div>
        <div class="modern-stats-label">卡密总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">3</div>
        <div class="modern-stats-label">最大层级</div>
    </div>
</div>

<!-- 分类列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <div class="d-flex justify-content-between align-items-center w-100">
            <div>
                <h5 class="modern-card-title">
                    <i class="fas fa-sitemap me-2"></i>
                    分类层级结构
                </h5>
                <small class="text-muted">点击箭头或分类名展开/收起子分类，悬停显示操作按钮</small>
            </div>
            <button class="modern-btn modern-btn-outline btn-sm" onclick="showHierarchyHelp()">
                <i class="fas fa-question-circle"></i>
                层级说明
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">

        <?php if(empty($categories) || (($categories instanceof \think\Collection || $categories instanceof \think\Paginator ) && $categories->isEmpty())): ?>
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-folder-open fa-3x mb-3 opacity-25"></i>
                <div class="h5">暂无分类数据</div>
                <p>开始创建您的第一个分类吧</p>
                <button class="modern-btn modern-btn-primary" onclick="showAddModal()">
                    <i class="fas fa-plus"></i>
                    添加分类
                </button>
            </div>
        </div>
        <?php else: ?>
        <div class="modern-table-container">
            <table class="modern-table category-table">
        <thead>
            <tr>
                <th width="8%">ID</th>
                <th width="35%">分类名称</th>
                <th width="20%">描述</th>
                <th width="8%">排序</th>
                <th width="8%">状态</th>
                <th width="8%">子分类</th>
                <th width="13%">操作</th>
            </tr>
        </thead>
        <tbody>
            <?php if(is_array($categoryTree) || $categoryTree instanceof \think\Collection || $categoryTree instanceof \think\Paginator): $i = 0; $__LIST__ = $categoryTree;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
            <!-- 一级分类 -->
            <tr class="category-row level-1"
                data-id="<?php echo htmlentities((string) $category['id']); ?>"
                data-level="1"
                data-parent="0"
                data-sort="<?php echo htmlentities((string) $category['sort_order']); ?>"
                onclick="selectCategory(<?php echo htmlentities((string) $category['id']); ?>)">

                <td>
                    <span class="category-id"><?php echo htmlentities((string) $category['id']); ?></span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow <?php if(empty($category['children'])): ?>no-children<?php endif; ?>"
                             onclick="event.stopPropagation(); toggleCategory(<?php echo htmlentities((string) $category['id']); ?>)">
                            <?php if(!empty($category['children'])): ?>
                            <i class="fas fa-chevron-right"></i>
                            <?php endif; ?>
                        </div>

                        <div class="category-icon level-1">
                            <i class="fas fa-cube"></i>
                        </div>

                        <div class="category-name-clickable" onclick="event.stopPropagation(); toggleCategory(<?php echo htmlentities((string) $category['id']); ?>)">
                            <span class="category-name-text"><?php echo htmlentities((string) $category['name']); ?></span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="<?php echo htmlentities((string) $category['description']); ?>"><?php echo !empty($category['description']) ? htmlentities((string) $category['description']) : '-'; ?></span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="<?php echo htmlentities((string) $category['sort_order']); ?>"
                               min="1"
                               max="999"
                               data-id="<?php echo htmlentities((string) $category['id']); ?>"
                               data-original="<?php echo htmlentities((string) $category['sort_order']); ?>"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-<?php echo $category['status']==1 ? 'enabled'  :  'disabled'; ?>">
                        <?php echo $category['status']==1 ? '启用'  :  '禁用'; ?>
                    </span>
                </td>

                <td>
                    <?php 
                        echo \app\model\Category::where('parent_id', $category['id'])->count();
                     ?>
                </td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); addSubCategory(<?php echo htmlentities((string) $category['id']); ?>)" title="添加子分类">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="action-btn edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $category['id']); ?>)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus(<?php echo htmlentities((string) $category['id']); ?>, <?php echo htmlentities((string) $category['status']); ?>)" title="<?php echo $category['status']==1 ? '禁用'  :  '启用'; ?>">
                            <i class="fas fa-<?php echo $category['status']==1 ? 'ban'  :  'check'; ?>"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $category['id']); ?>)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>

            <!-- 二级分类 -->
            <?php if(!empty($category['children'])): if(is_array($category['children']) || $category['children'] instanceof \think\Collection || $category['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $category['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$child): $mod = ($i % 2 );++$i;?>
            <tr class="category-row level-2 category-child collapsed"
                data-id="<?php echo htmlentities((string) $child['id']); ?>"
                data-level="2"
                data-parent="<?php echo htmlentities((string) $category['id']); ?>"
                data-sort="<?php echo htmlentities((string) $child['sort_order']); ?>"
                onclick="selectCategory(<?php echo htmlentities((string) $child['id']); ?>)">

                <td>
                    <span class="category-id"><?php echo htmlentities((string) $child['id']); ?></span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow <?php if(empty($child['children'])): ?>no-children<?php endif; ?>"
                             onclick="event.stopPropagation(); toggleCategory(<?php echo htmlentities((string) $child['id']); ?>)">
                            <?php if(!empty($child['children'])): ?>
                            <i class="fas fa-chevron-right"></i>
                            <?php endif; ?>
                        </div>

                        <div class="category-icon level-2">
                            <i class="fas fa-folder"></i>
                        </div>

                        <div class="category-name-clickable" onclick="event.stopPropagation(); toggleCategory(<?php echo htmlentities((string) $child['id']); ?>)">
                            <span class="category-name-text"><?php echo htmlentities((string) $child['name']); ?></span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="<?php echo htmlentities((string) $child['description']); ?>"><?php echo !empty($child['description']) ? htmlentities((string) $child['description']) : '-'; ?></span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="<?php echo htmlentities((string) $child['sort_order']); ?>"
                               min="1"
                               max="999"
                               data-id="<?php echo htmlentities((string) $child['id']); ?>"
                               data-original="<?php echo htmlentities((string) $child['sort_order']); ?>"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-<?php echo $child['status']==1 ? 'enabled'  :  'disabled'; ?>">
                        <?php echo $child['status']==1 ? '启用'  :  '禁用'; ?>
                    </span>
                </td>

                <td>
                    <?php 
                        echo \app\model\Category::where('parent_id', $child['id'])->count();
                     ?>
                </td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); addSubCategory(<?php echo htmlentities((string) $child['id']); ?>)" title="添加子分类">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="action-btn edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $child['id']); ?>)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus(<?php echo htmlentities((string) $child['id']); ?>, <?php echo htmlentities((string) $child['status']); ?>)" title="<?php echo $child['status']==1 ? '禁用'  :  '启用'; ?>">
                            <i class="fas fa-<?php echo $child['status']==1 ? 'ban'  :  'check'; ?>"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $child['id']); ?>)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>

            <!-- 三级分类 -->
            <?php if(!empty($child['children'])): if(is_array($child['children']) || $child['children'] instanceof \think\Collection || $child['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $child['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$grandchild): $mod = ($i % 2 );++$i;?>
            <tr class="category-row level-3 category-child collapsed"
                data-id="<?php echo htmlentities((string) $grandchild['id']); ?>"
                data-level="3"
                data-parent="<?php echo htmlentities((string) $child['id']); ?>"
                data-sort="<?php echo htmlentities((string) $grandchild['sort_order']); ?>"
                onclick="selectCategory(<?php echo htmlentities((string) $grandchild['id']); ?>)">

                <td>
                    <span class="category-id"><?php echo htmlentities((string) $grandchild['id']); ?></span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow no-children"></div>

                        <div class="category-icon level-3">
                            <i class="fas fa-file"></i>
                        </div>

                        <div class="category-name-clickable">
                            <span class="category-name-text"><?php echo htmlentities((string) $grandchild['name']); ?></span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="<?php echo htmlentities((string) $grandchild['description']); ?>"><?php echo !empty($grandchild['description']) ? htmlentities((string) $grandchild['description']) : '-'; ?></span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="<?php echo htmlentities((string) $grandchild['sort_order']); ?>"
                               min="1"
                               max="999"
                               data-id="<?php echo htmlentities((string) $grandchild['id']); ?>"
                               data-original="<?php echo htmlentities((string) $grandchild['sort_order']); ?>"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-<?php echo $grandchild['status']==1 ? 'enabled'  :  'disabled'; ?>">
                        <?php echo $grandchild['status']==1 ? '启用'  :  '禁用'; ?>
                    </span>
                </td>

                <td>-</td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $grandchild['id']); ?>)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus(<?php echo htmlentities((string) $grandchild['id']); ?>, <?php echo htmlentities((string) $grandchild['status']); ?>)" title="<?php echo $grandchild['status']==1 ? '禁用'  :  '启用'; ?>">
                            <i class="fas fa-<?php echo $grandchild['status']==1 ? 'ban'  :  'check'; ?>"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $grandchild['id']); ?>)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            <?php endif; ?>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            <?php endif; ?>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </tbody>
    </table>
        <?php endif; ?>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/components/form.js"></script>
<script src="/static/js/utils/validator.js"></script>
<script src="/static/js/modules/category/tree.js"></script>
<script src="/static/js/modules/category/operations.js"></script>
<script src="/static/js/modules/category/index.js"></script>
<script>









</script>

<!-- 层级说明模态框 -->
<div id="hierarchyModal" class="modal" style="display: none;" onclick="if(event.target === this) closeHierarchyModal()">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h2><i class="fas fa-sitemap"></i> 分类层级结构说明</h2>
            <span class="close" onclick="closeHierarchyModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="help-section">
                <h3><i class="fas fa-layer-group"></i> 分类层级说明</h3>
                <div class="hierarchy-levels">
                    <div class="level-item">
                        <span class="level-badge level-1">一级</span>
                        <div class="level-content">
                            <span class="level-desc">主分类 - 最多支持3级分类结构</span>
                            <span class="level-example">如：会员卡密、软件授权、数字内容</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-2">二级</span>
                        <div class="level-content">
                            <span class="level-desc">子分类 - 归属于一级分类</span>
                            <span class="level-example">如：基础会员、高级会员、开发工具</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-3">三级</span>
                        <div class="level-content">
                            <span class="level-desc">子子分类 - 归属于二级分类</span>
                            <span class="level-example">如：月度会员、年度会员、专业版</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="help-section">
                <h3><i class="fas fa-sort-numeric-down"></i> 排序功能说明</h3>
                <div class="sort-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-arrow-up text-primary"></i>
                            <div>
                                <strong>排序规则</strong>
                                <p>数字越小，排序越靠前</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-layer-group text-success"></i>
                            <div>
                                <strong>同级排序</strong>
                                <p>同级分类之间可以调整排序</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-save text-warning"></i>
                            <div>
                                <strong>自动保存</strong>
                                <p>修改后自动保存并刷新显示</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-hashtag text-info"></i>
                            <div>
                                <strong>数值范围</strong>
                                <p>排序值范围：1-999</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeHierarchyModal()">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>
</div>

<!-- 添加/编辑分类模态框 -->
<div id="categoryModal" class="modal" style="display: none;" onclick="if(event.target === this) closeCategoryModal()">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2 id="categoryModalTitle"><i class="fas fa-plus"></i> 添加分类</h2>
            <span class="close" onclick="closeCategoryModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="categoryModalForm">
                <input type="hidden" id="modal_category_id" name="id">

                <div class="form-group">
                    <label for="modal_parent_id">父分类</label>
                    <input type="hidden" id="modal_parent_id" name="parent_id" value="0">
                    <div class="category-tree-selector" id="categoryTreeSelector">
                        <div class="tree-selector-header" onclick="toggleTreeSelector()">
                            <span class="selected-text">顶级分类</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="treeSelectorDropdown" style="display: none;">
                            <div class="tree-item" data-id="0" onclick="selectCategory(0, '顶级分类', 0)">
                                <i class="fas fa-home text-primary"></i>
                                <span>顶级分类</span>
                            </div>
                        </div>
                    </div>
                    <small class="form-text">选择父分类，不选择则为顶级分类</small>
                </div>

                <div class="form-group">
                    <label for="modal_name">分类名称 <span style="color: #ff4d4f;">*</span></label>
                    <input type="text" id="modal_name" name="name" class="form-control" required maxlength="100">
                    <small class="form-text">分类名称，最多100个字符</small>
                </div>

                <div class="form-group">
                    <label for="modal_description">分类描述</label>
                    <textarea id="modal_description" name="description" class="form-control" rows="3" maxlength="500"></textarea>
                    <small class="form-text">分类的详细描述，最多500个字符</small>
                </div>

                <div class="form-group">
                    <label for="modal_sort_order">排序权重</label>
                    <input type="number" id="modal_sort_order" name="sort_order" class="form-control" value="0" min="0" max="9999">
                    <small class="form-text">数字越小排序越靠前，默认为0</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeCategoryModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" class="btn-primary" onclick="submitCategoryForm()">
                <span class="btn-text">
                    <i class="fas fa-save"></i> 保存
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 保存中...
                </span>
            </button>
        </div>
    </div>
</div>


</body>
</html>
