<?php

namespace app\controller;

use app\BaseController;
use app\model\Card as CardModel;
use app\model\Category;
use think\facade\Db;

/**
 * 卡密管理控制器
 */
class Card extends BaseController
{
    /**
     * 卡密列表页面
     */
    public function index()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $status = $this->request->param('status', '');
        $category_id = $this->request->param('category_id', '');
        $keyword = $this->request->param('keyword', '');

        // 限制每页显示数量的范围
        $limit = max(5, min(100, intval($limit)));
        
        // 构建查询条件
        $where = [];
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        if ($category_id !== '') {
            $where[] = ['category_id', '=', $category_id];
        }
        if ($keyword !== '') {
            $where[] = ['card_code', 'like', '%' . $keyword . '%'];
        }
        
        // 查询卡密列表
        $cards = CardModel::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.*',
                'cat.name as category_name'
            ])
            ->where($where)
            ->order('c.created_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page,
                'path' => $this->request->baseUrl(),
                'query' => $this->request->param()
            ]);
        
        // 获取分类列表（树形结构）
        $categories = Category::getCategoryTree();
        
        return view('card/index', [
            'cards' => $cards,
            'categories' => $categories,
            'filters' => [
                'status' => $status,
                'category_id' => $category_id,
                'keyword' => $keyword,
                'limit' => $limit
            ]
        ]);
    }
    
    /**
     * 批量生成卡密页面
     */
    public function generate()
    {
        if ($this->request->isPost()) {
            return $this->doGenerate();
        }
        
        // 获取分类列表（层级结构）
        $categories = Category::getFlatCategories();
        return view('card/generate', ['categories' => $categories]);
    }
    
    /**
     * 执行批量生成卡密
     */
    private function doGenerate()
    {
        // 获取请求数据（支持JSON和表单数据）
        $contentType = $this->request->header('content-type');
        if (strpos($contentType, 'application/json') !== false) {
            $data = json_decode($this->request->getContent(), true);
        } else {
            $data = $this->request->post();
        }
        
        // 验证参数
        if (empty($data['category_id']) || empty($data['count']) || empty($data['content'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }
        
        $categoryId = (int)$data['category_id'];
        $count = (int)$data['count'];
        $content = trim($data['content']);
        $expireAt = !empty($data['expire_at']) ? $data['expire_at'] : null;
        
        // 验证分类是否存在
        $category = Category::find($categoryId);
        if (!$category || $category->status != Category::STATUS_ENABLED) {
            return json(['code' => 400, 'message' => '分类不存在或已禁用']);
        }
        
        // 验证生成数量
        if ($count < 1 || $count > 1000) {
            return json(['code' => 400, 'message' => '生成数量必须在1-1000之间']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            $cards = [];
            for ($i = 0; $i < $count; $i++) {
                $cardCode = CardModel::generateUniqueCardCode();
                $cards[] = [
                    'card_code' => $cardCode,
                    'category_id' => $categoryId,
                    'status' => CardModel::STATUS_UNUSED,
                    'content' => $content,
                    'expire_at' => $expireAt,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            }
            
            // 批量插入
            CardModel::insertAll($cards);
            
            Db::commit();
            return json(['code' => 200, 'message' => "成功生成 {$count} 个卡密"]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '生成失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 更新卡密状态
     */
    public function updateStatus()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        if (empty($id) || !in_array($status, [CardModel::STATUS_UNUSED, CardModel::STATUS_DISABLED])) {
            return json(['code' => 400, 'message' => '参数错误']);
        }
        
        $card = CardModel::find($id);
        if (!$card) {
            return json(['code' => 404, 'message' => '卡密不存在']);
        }
        
        // 已使用的卡密不能修改状态
        if ($card->status == CardModel::STATUS_USED) {
            return json(['code' => 400, 'message' => '已使用的卡密不能修改状态']);
        }
        
        $card->status = $status;
        if ($card->save()) {
            return json(['code' => 200, 'message' => '状态更新成功']);
        } else {
            return json(['code' => 500, 'message' => '状态更新失败']);
        }
    }
    
    /**
     * 批量删除卡密
     */
    public function batchDelete()
    {
        $ids = $this->request->post('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return json(['code' => 400, 'message' => '请选择要删除的卡密']);
        }
        
        // 检查是否有已使用的卡密
        $usedCount = CardModel::whereIn('id', $ids)
            ->where('status', CardModel::STATUS_USED)
            ->count();
        
        if ($usedCount > 0) {
            return json(['code' => 400, 'message' => '不能删除已使用的卡密']);
        }
        
        Db::startTrans();
        try {
            CardModel::whereIn('id', $ids)->delete();
            Db::commit();
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '删除失败']);
        }
    }
    
    /**
     * 导出卡密
     */
    public function export()
    {
        $password = $this->request->post('password');
        $exportPassword = \app\model\SystemSetting::getValue('export_password');
        
        if (empty($password) || $password !== $exportPassword) {
            return json(['code' => 400, 'message' => '导出密码错误']);
        }
        
        $status = $this->request->post('status', '');
        $categoryId = $this->request->post('category_id', '');
        
        // 构建查询条件
        $where = [];
        if ($status !== '') {
            $where[] = ['c.status', '=', $status];
        }
        if ($categoryId !== '') {
            $where[] = ['c.category_id', '=', $categoryId];
        }
        
        // 查询数据
        $cards = CardModel::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.card_code',
                'cat.name as category_name',
                'c.status',
                'c.content',
                'c.created_at',
                'c.used_at',
                'c.expire_at'
            ])
            ->where($where)
            ->order('c.created_at', 'desc')
            ->select();
        
        // 生成CSV内容
        $csv = "卡密编号,分类,状态,内容,创建时间,使用时间,过期时间\n";
        foreach ($cards as $card) {
            $statusText = match($card['status']) {
                CardModel::STATUS_UNUSED => '未使用',
                CardModel::STATUS_USED => '已使用',
                CardModel::STATUS_DISABLED => '已禁用',
                default => '未知'
            };
            
            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s\n",
                $card['card_code'],
                $card['category_name'],
                $statusText,
                str_replace(["\r", "\n", ","], ["", "", "，"], $card['content']),
                $card['created_at'],
                $card['used_at'] ?: '',
                $card['expire_at'] ?: ''
            );
        }
        
        // 设置响应头
        $filename = '卡密导出_' . date('YmdHis') . '.csv';
        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * 获取分类数据（用于模态框）
     */
    public function getCategories()
    {
        try {
            $categories = Category::getFlatCategories();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类数据失败：' . $e->getMessage()
            ]);
        }
    }
}
