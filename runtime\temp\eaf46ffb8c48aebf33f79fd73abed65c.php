<?php /*a:2:{s:44:"F:\linshi\thphp\kmxt\view\content\index.html";i:1754054162;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754043981;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容管理</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-active: var(--primary-color);
            --content-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            overflow-y: auto;
            will-change: transform;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0;
            will-change: margin-left;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 内容容器 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }

        /* 响应式内边距 */
        @media (max-width: 1200px) {
            .content-wrapper {
                padding: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .content-wrapper {
                padding: 0.75rem;
            }
        }

        /* 现代化卡片样式 */
        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 特殊情况：包含下拉框的卡片需要允许内容溢出 */
        .modern-card.compact-filter {
            overflow: visible;
        }

        .modern-card.compact-filter .modern-card-body {
            overflow: visible;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .modern-card-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modern-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid var(--border-color);
        }

        /* 现代化按钮样式 */
        .modern-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius-lg);
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-outline {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .modern-btn-outline:hover {
            background: white;
            color: var(--text-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 现代化表单样式 */
        .modern-form-group {
            margin-bottom: 1.5rem;
        }

        .modern-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .modern-form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .modern-form-control::placeholder {
            color: var(--text-muted);
        }

        /* 现代化表格样式 */
        .modern-table-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            font-size: 0.875rem;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 现代化状态标签 */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .modern-badge-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .modern-badge-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .modern-badge-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .modern-badge-primary {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        /* 现代化统计卡片 */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .modern-stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .modern-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .modern-stats-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem auto;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .modern-stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .modern-stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .modern-stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .modern-stats-trend.positive {
            color: var(--success-color);
        }

        .modern-stats-trend.negative {
            color: var(--error-color);
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">内容管理</h1>
        <p class="text-muted mb-0">管理系统中的所有内容，内容可用于生成卡密</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button type="button" class="modern-btn modern-btn-primary" onclick="showCreateModal()">
            <i class="fas fa-plus"></i>
            创建内容
        </button>
    </div>
</div>

<!-- 统计概况 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-file-alt"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['total']); ?></div>
        <div class="modern-stats-label">内容总数</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['enabled']); ?></div>
        <div class="modern-stats-label">已启用</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-pause-circle"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['disabled']); ?></div>
        <div class="modern-stats-label">已禁用</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) count($stats['category_stats'])); ?></div>
        <div class="modern-stats-label">涉及分类</div>
    </div>
</div>

<!-- 筛选工具栏 -->
<div class="modern-card filter-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h5>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <div class="modern-form-group mb-0">
                        <div class="d-flex align-items-center gap-2">
                            <label class="modern-form-label mb-0" style="white-space: nowrap; min-width: 60px;">分类：</label>
                            <div class="filter-category-tree-selector" style="flex: 1;">
                                <div class="filter-tree-selector-input" onclick="toggleFilterCategoryDropdown()">
                                    <span class="selected-text" id="filterSelectedCategoryText">
                                        <?php if($filters['category_id']): 
                                                $selectedCategory = null;
                                                foreach ($categories as $cat) {
                                                    if ($cat['id'] == $filters['category_id']) {
                                                        $selectedCategory = $cat;
                                                        break;
                                                    }
                                                }
                                                echo $selectedCategory ? \app\model\Content::getCategoryPath($selectedCategory['id']) : '全部分类';
                                             else: ?>
                                            全部分类
                                        <?php endif; ?>
                                    </span>
                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                </div>
                                <div class="filter-tree-selector-dropdown" id="filterCategoryDropdown" style="display: none;">
                                    <div class="filter-tree-item" onclick="selectFilterCategory('', '全部分类')">
                                        <div class="filter-tree-item-content">
                                            <span class="expand-icon-placeholder"></span>
                                            <div class="category-icon all-categories-icon">
                                                <i class="fas fa-list"></i>
                                            </div>
                                            <span>全部分类</span>
                                        </div>
                                    </div>
                                    <!-- 分类树将通过JavaScript动态加载 -->
                                </div>
                                <input type="hidden" name="category_id" value="<?php echo htmlentities((string) $filters['category_id']); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group mb-0">
                        <div class="d-flex align-items-center gap-2">
                            <label class="modern-form-label mb-0" style="white-space: nowrap; min-width: 50px;">状态：</label>
                            <select name="status" class="modern-form-control">
                                <option value="">全部状态</option>
                                <option value="1" <?php echo $filters['status']==='1' ? 'selected'  :  ''; ?>>已启用</option>
                                <option value="0" <?php echo $filters['status']==='0' ? 'selected'  :  ''; ?>>已禁用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="modern-form-group mb-0">
                        <div class="d-flex align-items-center gap-2">
                            <label class="modern-form-label mb-0" style="white-space: nowrap; min-width: 80px;">关键词：</label>
                            <input type="text" name="keyword" class="modern-form-control" placeholder="搜索标题或内容..." value="<?php echo htmlentities((string) $filters['keyword']); ?>">
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="modern-form-group mb-0">
                        <div class="d-flex gap-2">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <a href="/content" class="modern-btn modern-btn-outline">
                                <i class="fas fa-undo"></i>
                                重置
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 内容列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            内容列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" id="selectAllBtn" class="modern-btn modern-btn-outline btn-sm" onclick="toggleSelectAllBtn()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="batchUpdateSort()">
                <i class="fas fa-sort"></i>
                批量排序
            </button>
            <button type="button" class="modern-btn modern-btn-danger btn-sm" onclick="batchDelete()">
                <i class="fas fa-trash"></i>
                批量删除
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>内容标题</th>
                        <th>分类</th>
                        <th>状态</th>
                        <th>排序</th>
                        <th>创建时间</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(is_array($contents) || $contents instanceof \think\Collection || $contents instanceof \think\Paginator): $i = 0; $__LIST__ = $contents;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$content): $mod = ($i % 2 );++$i;?>
                    <tr data-id="<?php echo htmlentities((string) $content['id']); ?>" class="sortable-row">
                        <td>
                            <input type="checkbox" class="content-checkbox" value="<?php echo htmlentities((string) $content['id']); ?>" onchange="updateSelectAllButton()">
                        </td>
                        <td>
                            <div class="content-title-cell">
                                <div class="content-title" onclick="showContentModal('<?php echo htmlentities((string) $content['id']); ?>', '<?php echo htmlentities((string) $content['title']); ?>', `<?php echo $content['content']; ?>`)" title="点击查看完整内容">
                                    <div class="fw-bold text-dark mb-1"><?php echo htmlentities((string) $content['title']); ?></div>
                                    <div class="text-muted small text-truncate" style="max-width: 300px;">
                                        <?php echo htmlentities((string) mb_substr($content['content'],0,50,'utf-8')); if(mb_strlen($content['content'], 'utf-8') > 50): ?>...<?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="modern-badge modern-badge-primary" title="<?php echo htmlentities((string) $content['category_path']); ?>"><?php echo htmlentities((string) $content['category_path']); ?></span>
                        </td>
                        <td>
                            <?php if($content['status'] == 1): ?>
                                <span class="modern-badge modern-badge-success">
                                    <i class="fas fa-check-circle"></i>
                                    已启用
                                </span>
                            <?php else: ?>
                                <span class="modern-badge modern-badge-warning">
                                    <i class="fas fa-pause-circle"></i>
                                    已禁用
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="sort-input-container">
                                <input type="number" class="form-control form-control-sm sort-input"
                                       value="<?php echo htmlentities((string) (isset($content['sort_order']) && ($content['sort_order'] !== '')?$content['sort_order']:0)); ?>"
                                       min="0" max="9999"
                                       onchange="updateSort(<?php echo htmlentities((string) $content['id']); ?>, this.value)"
                                       data-original="<?php echo htmlentities((string) (isset($content['sort_order']) && ($content['sort_order'] !== '')?$content['sort_order']:0)); ?>">
                                <div class="sort-controls-inline">
                                    <button type="button" class="sort-btn sort-btn-up" onclick="moveUp(<?php echo htmlentities((string) $content['id']); ?>)" title="上移">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <button type="button" class="sort-btn sort-btn-down" onclick="moveDown(<?php echo htmlentities((string) $content['id']); ?>)" title="下移">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td class="text-muted">
                            <small><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric((isset($content['created_at']) && ($content['created_at'] !== '')?$content['created_at']:'-'))? strtotime((isset($content['created_at']) && ($content['created_at'] !== '')?$content['created_at']:'-')) : (isset($content['created_at']) && ($content['created_at'] !== '')?$content['created_at']:'-'))); ?></small>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="showEditModal(<?php echo htmlentities((string) $content['id']); ?>)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="toggleStatus(<?php echo htmlentities((string) $content['id']); ?>)" title="切换状态">
                                    <i class="fas fa-<?php echo $content['status']==1 ? 'pause'  :  'play'; ?>"></i>
                                </button>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="deleteContent(<?php echo htmlentities((string) $content['id']); ?>)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; if(empty($contents) || (($contents instanceof \think\Collection || $contents instanceof \think\Paginator ) && $contents->isEmpty())): ?>
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div class="h5">暂无内容数据</div>
                                <p>点击上方"创建内容"按钮开始创建第一个内容</p>
                                <button type="button" class="modern-btn modern-btn-primary" onclick="showCreateModal()">
                                    <i class="fas fa-plus"></i>
                                    创建内容
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if($contents->total() > 0): ?>
    <div class="modern-card-footer">
        <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
            <div class="pagination-info d-flex align-items-center gap-3">
                <div class="text-muted">
                    显示第 <?php echo htmlentities((string) $contents->listRows() * ($contents->currentPage() - 1) + 1); ?> - <?php echo htmlentities((string) $contents->listRows() * ($contents->currentPage() - 1) + $contents->count()); ?> 条，
                    共 <?php echo htmlentities((string) $contents->total()); ?> 条记录
                </div>
                <div class="page-size-selector d-flex align-items-center gap-2">
                    <label class="text-muted mb-0">每页显示：</label>
                    <select class="form-select form-select-sm" onchange="changePageSize(this.value)" style="width: 80px;">
                        <option value="10" <?php echo app('request')->param('limit')==10 || !app('request')->param('limit')?'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo app('request')->param('limit')==20?'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo app('request')->param('limit')==50?'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo app('request')->param('limit')==100?'selected' : ''; ?>>100</option>
                    </select>
                </div>
            </div>
            <div class="pagination-controls">
                <?php if($contents->hasPages()): ?>
                <nav aria-label="分页导航">
                    <ul class="pagination pagination-sm mb-0">
                        <!-- 上一页 -->
                        <?php if($contents->currentPage() > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo url('content/index', array_merge(request()->param(), ['page' => $contents->currentPage()-1])); ?>" aria-label="上一页">
                                <span aria-hidden="true">«</span>
                            </a>
                        </li>
                        <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link">«</span>
                        </li>
                        <?php endif; ?>

                        <!-- 页码 -->
                        <?php 
                        $currentPage = $contents->currentPage();
                        $lastPage = $contents->lastPage();
                        $start = max(1, $currentPage - 2);
                        $end = min($lastPage, $currentPage + 2);

                        // 如果当前页靠近开始，显示更多后面的页码
                        if ($currentPage <= 3) {
                            $end = min($lastPage, 5);
                        }

                        // 如果当前页靠近结束，显示更多前面的页码
                        if ($currentPage >= $lastPage - 2) {
                            $start = max(1, $lastPage - 4);
                        }
                         ?>

                        <!-- 显示第一页和省略号 -->
                        <?php if($start > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo url('content/index', array_merge(request()->param(), ['page' => 1])); ?>">1</a>
                        </li>
                        <?php if($start > 2): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>

                        <!-- 页码范围 -->
                        <?php $__FOR_START_1146799475__=$start;$__FOR_END_1146799475__=$end;for($i=$__FOR_START_1146799475__;$i < $__FOR_END_1146799475__;$i+=1){ if($i == $contents->currentPage()): ?>
                        <li class="page-item active">
                            <span class="page-link"><?php echo htmlentities((string) $i); ?></span>
                        </li>
                        <?php else: ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo url('content/index', array_merge(request()->param(), ['page' => $i])); ?>"><?php echo htmlentities((string) $i); ?></a>
                        </li>
                        <?php endif; } ?>

                        <!-- 显示最后一页和省略号 -->
                        <?php if($end < $contents->lastPage()): if($end < $contents->lastPage() - 1): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo url('content/index', array_merge(request()->param(), ['page' => $contents->lastPage()])); ?>"><?php echo htmlentities((string) $contents->lastPage()); ?></a>
                        </li>
                        <?php endif; ?>

                        <!-- 下一页 -->
                        <?php if($contents->currentPage() < $contents->lastPage()): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo url('content/index', array_merge(request()->param(), ['page' => $contents->currentPage()+1])); ?>" aria-label="下一页">
                                <span aria-hidden="true">»</span>
                            </a>
                        </li>
                        <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link">»</span>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 内容预览模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalTitle">内容详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="contentModalBody" style="white-space: pre-wrap;"></div>
            </div>
        </div>
    </div>
</div>

<style>
/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    overflow: hidden;
}

/* 确保模态框显示时页面不滚动 */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

.modal-content {
    background-color: white;
    margin: 3vh auto;
    padding: 0;
    border-radius: 16px;
    width: 85%;
    max-width: 700px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
    height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    top: 0;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.modal-header h2 i {
    margin-right: 12px;
    color: rgba(255, 255, 255, 0.9);
}

.close {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close:hover {
    color: white;
}

.modal-body {
    padding: 24px 32px;
    flex: 1;
    overflow-y: visible;
    display: flex;
    flex-direction: column;
}

.modal-footer {
    padding: 20px 32px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.form-group {
    margin-bottom: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 0;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    white-space: nowrap;
    min-width: 80px;
    padding-top: 10px;
}

.form-group .form-field {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    flex-direction: column;
}

.form-group.full-width label {
    margin-bottom: 8px;
    padding-top: 0;
}

.form-group.full-width .form-field {
    width: 100%;
}

.form-group.full-width .form-control {
    width: 100%;
}

.modal .form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.modal .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-row .form-group {
    flex: 1;
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.loading-spinner {
    display: none;
}

/* 分类树选择器样式 */
.category-tree-selector {
    position: relative;
}

.tree-selector-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tree-selector-input:hover {
    border-color: var(--primary-color);
}

.tree-selector-input.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tree-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 10001;
    max-height: 300px;
    overflow-y: auto;
}

.tree-item {
    position: relative;
}

.tree-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.tree-item-content:hover {
    background-color: #f8f9fa;
}

.expand-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
    color: #6c757d;
}

.expand-icon-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.category-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 10px;
}

.level-1-icon {
    background: #e3f2fd !important;
    color: #1976d2 !important;
}

.level-2-icon {
    background: #e8f5e8 !important;
    color: #4caf50 !important;
    border-radius: 4px !important;
}

.level-3-icon {
    background: #f3e5f5 !important;
    color: #9c27b0 !important;
}

.tree-item.level-1 > .tree-item-content {
    padding-left: 0.75rem !important;
}

.tree-item.level-2 > .tree-item-content {
    padding-left: 2rem !important;
}

.tree-item.level-3 > .tree-item-content {
    padding-left: 3rem !important;
    font-size: 0.875rem;
}

.tree-children {
    display: none;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

/* 筛选器分类树选择器样式 */
.filter-category-tree-selector {
    position: relative;
    z-index: 1050;
}

/* 确保筛选条件卡片不会裁剪下拉框 */
.modern-card {
    overflow: visible !important;
}

.modern-card-body {
    overflow: visible !important;
}

/* 筛选条件卡片特殊层级 */
.modern-card:has(.filter-category-tree-selector) {
    z-index: 1040 !important;
    position: relative;
}

/* 如果浏览器不支持:has选择器，使用类名方式 */
.filter-card {
    z-index: 1040 !important;
    position: relative;
}

/* 确保内容列表卡片层级较低 */
.modern-card:not(.filter-card) {
    z-index: 1 !important;
    position: relative;
}

.filter-tree-selector-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 38px;
}

.filter-tree-selector-input:hover {
    border-color: var(--primary-color);
}

.filter-tree-selector-input.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filter-tree-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1049;
    max-height: 300px;
    overflow-y: auto;
}

.filter-tree-item {
    position: relative;
}

.filter-tree-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.filter-tree-item-content:hover {
    background-color: #f8f9fa;
}

.all-categories-icon {
    background: #e3f2fd !important;
    color: #1976d2 !important;
    border-radius: 50% !important;
}

/* 筛选器树形结构层级样式 */
.filter-tree-item.level-1 > .filter-tree-item-content {
    padding-left: 0.75rem !important;
}

.filter-tree-item.level-2 > .filter-tree-item-content {
    padding-left: 2rem !important;
}

.filter-tree-item.level-3 > .filter-tree-item-content {
    padding-left: 3rem !important;
    font-size: 0.875rem;
}

/* 筛选器树形子项容器 */
.filter-tree-item .tree-children {
    display: none;
}

/* 排序输入框容器样式 */
.sort-input-container {
    position: relative;
    display: inline-block;
    width: 80px;
}

.sort-input-container .sort-input {
    width: 100%;
    padding-right: 20px;
    text-align: center;
}

.sort-controls-inline {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.sort-btn {
    background: none;
    border: none;
    padding: 1px 2px;
    font-size: 8px;
    line-height: 1;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;
    width: 14px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sort-btn:hover {
    color: var(--primary-color);
    background-color: rgba(24, 144, 255, 0.1);
}

.sort-btn i {
    font-size: 8px;
}

/* 分页组件样式 */
.modern-card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 16px 24px;
    border-radius: 0 0 12px 12px;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-size-selector label {
    font-size: 14px;
    white-space: nowrap;
}

.page-size-selector .form-select {
    min-width: 70px;
}

.pagination-controls .pagination {
    margin: 0;
    gap: 4px;
}

.pagination-controls .page-item {
    margin: 0 2px;
}

.pagination-controls .page-link {
    color: #6c757d;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    transition: all 0.2s ease;
}

.pagination-controls .page-link:hover {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    text-decoration: none;
}

.pagination-controls .page-item.active .page-link {
    background-color: #6366f1;
    border-color: #6366f1;
    color: #fff;
    font-weight: 600;
}

.pagination-controls .page-item.active .page-link:hover {
    background-color: #5855eb;
    border-color: #5855eb;
    color: #fff;
}

.pagination-controls .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* 响应式分页 */
@media (max-width: 768px) {
    .modern-card-footer .d-flex {
        flex-direction: column;
        align-items: stretch !important;
        gap: 1rem;
    }

    .pagination-info {
        justify-content: center;
        text-align: center;
    }

    .pagination-controls {
        display: flex;
        justify-content: center;
    }

    .pagination-controls .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        margin: 1vh auto;
        width: 95%;
        height: 95vh;
    }

    .modal-header {
        padding: 16px 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 12px 20px;
        flex-direction: column;
        gap: 8px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-group {
        margin-bottom: 10px;
        flex-direction: column;
        align-items: stretch;
    }

    .form-group label {
        margin-bottom: 6px;
        padding-top: 0;
        min-width: auto;
    }

    .form-group.full-width {
        flex-direction: column;
    }
}
</style>

<!-- 创建内容模态框 -->
<div id="createModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h2><i class="fas fa-plus"></i> 创建内容</h2>
            <span class="close" onclick="closeCreateModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="createForm">
                <div class="form-group">
                    <label for="create_category_id">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field">
                        <div class="category-tree-selector">
                            <div class="tree-selector-input" onclick="toggleCreateCategoryDropdown()">
                                <span class="selected-text" id="createSelectedCategoryText">请选择分类</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="tree-selector-dropdown" id="createCategoryDropdown" style="display: none;">
                                <!-- 分类树将通过JavaScript动态加载 -->
                            </div>
                        </div>
                        <input type="hidden" id="create_category_id" name="category_id" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="create_title">标题 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field">
                        <input type="text" id="create_title" name="title" class="form-control" required maxlength="255">
                        <small class="form-text">内容的标题，最多255个字符</small>
                    </div>
                </div>

                <div class="form-group full-width" style="flex: 1;">
                    <label for="create_content">内容 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field" style="flex: 1; display: flex; flex-direction: column; width: 100%;">
                        <textarea id="create_content" name="content" class="form-control" style="flex: 1; min-height: 180px; resize: vertical; width: 100%; box-sizing: border-box;" required maxlength="10000"></textarea>
                        <small class="form-text">内容详情，最多10000个字符</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="create_sort_order">排序</label>
                        <div class="form-field">
                            <input type="number" id="create_sort_order" name="sort_order" class="form-control" value="0">
                            <small class="form-text">数值越大排序越靠前</small>
                        </div>
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="create_status">状态</label>
                        <div class="form-field">
                            <select id="create_status" name="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeCreateModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="createSubmitBtn" class="btn-primary" onclick="submitCreate()">
                <span class="btn-text">
                    <i class="fas fa-save"></i> 创建
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 创建中...
                </span>
            </button>
        </div>
    </div>
</div>

<!-- 编辑内容模态框 -->
<div id="editModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h2><i class="fas fa-edit"></i> 编辑内容</h2>
            <span class="close" onclick="closeEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="editForm">
                <input type="hidden" id="edit_id" name="id">
                <div class="form-group">
                    <label for="edit_category_id">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field">
                        <div class="category-tree-selector">
                            <div class="tree-selector-input" onclick="toggleEditCategoryDropdown()">
                                <span class="selected-text" id="editSelectedCategoryText">请选择分类</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="tree-selector-dropdown" id="editCategoryDropdown" style="display: none;">
                                <!-- 分类树将通过JavaScript动态加载 -->
                            </div>
                        </div>
                        <input type="hidden" id="edit_category_id" name="category_id" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit_title">标题 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field">
                        <input type="text" id="edit_title" name="title" class="form-control" required maxlength="255">
                        <small class="form-text">内容的标题，最多255个字符</small>
                    </div>
                </div>

                <div class="form-group full-width" style="flex: 1;">
                    <label for="edit_content">内容 <span style="color: #ff4d4f;">*</span></label>
                    <div class="form-field" style="flex: 1; display: flex; flex-direction: column; width: 100%;">
                        <textarea id="edit_content" name="content" class="form-control" style="flex: 1; min-height: 180px; resize: vertical; width: 100%; box-sizing: border-box;" required maxlength="10000"></textarea>
                        <small class="form-text">内容详情，最多10000个字符</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="edit_sort_order">排序</label>
                        <div class="form-field">
                            <input type="number" id="edit_sort_order" name="sort_order" class="form-control">
                            <small class="form-text">数值越大排序越靠前</small>
                        </div>
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="edit_status">状态</label>
                        <div class="form-field">
                            <select id="edit_status" name="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeEditModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="editSubmitBtn" class="btn-primary" onclick="submitEdit()">
                <span class="btn-text">
                    <i class="fas fa-save"></i> 更新
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 更新中...
                </span>
            </button>
        </div>
    </div>
</div>

<script>
// 显示创建内容模态框
function showCreateModal() {
    // 先获取分类数据
    fetch('/cards/getCategories')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                populateCreateCategories(data.data);
                document.getElementById('createModal').style.display = 'block';
                document.body.classList.add('modal-open');
                // 重置表单
                document.getElementById('createForm').reset();
                document.getElementById('createSelectedCategoryText').textContent = '请选择分类';
                document.getElementById('create_category_id').value = '';
            } else {
                showToast('获取分类数据失败', 'error');
            }
        })
        .catch(error => {
            showToast('获取分类数据失败', 'error');
        });
}

// 关闭创建内容模态框
function closeCreateModal() {
    document.getElementById('createModal').style.display = 'none';
    document.body.classList.remove('modal-open');
    document.getElementById('createCategoryDropdown').style.display = 'none';
}

// 显示编辑内容模态框
function showEditModal(id) {
    // 先获取分类数据和内容数据
    Promise.all([
        fetch('/cards/getCategories').then(response => response.json()),
        fetch(`/content/getData?id=${id}`).then(response => response.json())
    ])
    .then(([categoriesData, contentData]) => {
        if (categoriesData.code === 200 && contentData.code === 200) {
            populateEditCategories(categoriesData.data);

            const content = contentData.data;
            document.getElementById('edit_id').value = content.id;
            document.getElementById('edit_title').value = content.title;
            document.getElementById('edit_content').value = content.content;
            document.getElementById('edit_sort_order').value = content.sort_order;
            document.getElementById('edit_status').value = content.status;
            document.getElementById('edit_category_id').value = content.category_id;

            // 设置分类显示文本 - 使用服务器返回的完整路径
            document.getElementById('editSelectedCategoryText').textContent = content.category_path || '请选择分类';

            document.getElementById('editModal').style.display = 'block';
            document.body.classList.add('modal-open');
        } else {
            showToast('获取数据失败', 'error');
        }
    })
    .catch(error => {
        showToast('获取数据失败', 'error');
    });
}

// 关闭编辑内容模态框
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    document.body.classList.remove('modal-open');
    document.getElementById('editCategoryDropdown').style.display = 'none';
}

// 显示内容模态框
function showContentModal(id, title, content) {
    document.getElementById('contentModalTitle').textContent = title;
    document.getElementById('contentModalBody').textContent = content;
    new bootstrap.Modal(document.getElementById('contentModal')).show();
}

// 其他JavaScript函数...
function updateSort(id, sortOrder) {
    // 显示加载状态
    const input = document.querySelector(`input[onchange*="${id}"]`);
    if (input) {
        input.disabled = true;
        input.style.opacity = '0.6';
    }

    fetch('/content/updateSort', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            id: id,
            sort_order: sortOrder
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 成功后无感刷新页面
            refreshContentList();
        } else {
            showToast(data.message || '排序更新失败', 'error');
            // 恢复输入框状态
            if (input) {
                input.disabled = false;
                input.style.opacity = '1';
            }
        }
    })
    .catch(error => {
        showToast('排序更新失败', 'error');
        // 恢复输入框状态
        if (input) {
            input.disabled = false;
            input.style.opacity = '1';
        }
    });
}

// 无感刷新内容列表
function refreshContentList() {
    // 获取当前页面的查询参数
    const urlParams = new URLSearchParams(window.location.search);
    const currentUrl = window.location.pathname + '?' + urlParams.toString();

    // 使用fetch获取新的页面内容
    fetch(currentUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 解析新的HTML
        const parser = new DOMParser();
        const newDoc = parser.parseFromString(html, 'text/html');

        // 只更新内容列表部分
        const currentTable = document.querySelector('.modern-table tbody');
        const newTable = newDoc.querySelector('.modern-table tbody');

        if (currentTable && newTable) {
            currentTable.innerHTML = newTable.innerHTML;
        }

        // 更新统计数据
        const currentStats = document.querySelector('.modern-stats-grid');
        const newStats = newDoc.querySelector('.modern-stats-grid');

        if (currentStats && newStats) {
            currentStats.innerHTML = newStats.innerHTML;
        }
    })
    .catch(error => {
        console.error('刷新失败:', error);
        // 如果无感刷新失败，则进行完整页面刷新
        location.reload();
    });
}

function moveUp(id) {
    const input = document.querySelector(`input[onchange*="${id}"]`);
    if (input) {
        const currentValue = parseInt(input.value) || 0;
        const newValue = currentValue + 1;
        input.value = newValue;
        updateSort(id, newValue);
    }
}

function moveDown(id) {
    const input = document.querySelector(`input[onchange*="${id}"]`);
    if (input) {
        const currentValue = parseInt(input.value) || 0;
        const newValue = Math.max(0, currentValue - 1);
        input.value = newValue;
        updateSort(id, newValue);
    }
}

function toggleStatus(id) {
    fetch('/content/toggleStatus?id=' + id, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showToast(data.message, 'success');
            // 无感刷新
            refreshContentList();
        } else {
            showToast(data.message || '操作失败', 'error');
        }
    });
}

function deleteContent(id) {
    if (!confirm('确定要删除这个内容吗？删除后无法恢复！')) {
        return;
    }

    // 创建FormData对象传递POST参数
    const formData = new FormData();
    formData.append('id', id);

    fetch('/content/delete', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showToast('删除成功', 'success');
            // 无感刷新
            refreshContentList();
        } else {
            showToast(data.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('删除错误:', error);
        showToast('删除失败，请重试', 'error');
    });
}

function toggleSelectAllBtn() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const icon = selectAllBtn.querySelector('i');
    const text = selectAllBtn.childNodes[2]; // 获取文本节点

    if (selectAllCheckbox.checked) {
        // 当前是全选状态，点击后取消全选
        selectAllCheckbox.checked = false;
        icon.className = 'fas fa-check-square';
        text.textContent = '全选';
    } else {
        // 当前是未全选状态，点击后全选
        selectAllCheckbox.checked = true;
        icon.className = 'fas fa-minus-square';
        text.textContent = '取消全选';
    }

    toggleSelectAll();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.content-checkbox');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const icon = selectAllBtn.querySelector('i');
    const text = selectAllBtn.childNodes[2]; // 获取文本节点

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    // 更新按钮状态
    if (selectAllCheckbox.checked) {
        icon.className = 'fas fa-minus-square';
        text.textContent = '取消全选';
    } else {
        icon.className = 'fas fa-check-square';
        text.textContent = '全选';
    }
}

// 监听单个复选框变化，更新全选按钮状态
function updateSelectAllButton() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.content-checkbox');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const icon = selectAllBtn.querySelector('i');
    const text = selectAllBtn.childNodes[2];

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    const totalCount = checkboxes.length;

    if (checkedCount === totalCount && totalCount > 0) {
        // 全部选中
        selectAllCheckbox.checked = true;
        icon.className = 'fas fa-minus-square';
        text.textContent = '取消全选';
    } else {
        // 部分选中或未选中
        selectAllCheckbox.checked = false;
        icon.className = 'fas fa-check-square';
        text.textContent = '全选';
    }
}

// 批量删除功能
function batchDelete() {
    const checkboxes = document.querySelectorAll('.content-checkbox:checked');
    if (checkboxes.length === 0) {
        showToast('请先选择要删除的内容', 'warning');
        return;
    }

    const ids = Array.from(checkboxes).map(cb => cb.value);
    const count = ids.length;

    if (!confirm(`确定要删除选中的 ${count} 条内容吗？删除后不可恢复！`)) {
        return;
    }

    // 显示加载状态
    const loadingToast = showToast('正在删除...', 'info', 0);

    fetch('/content/batchDelete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            ids: ids
        })
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.remove();
        }

        if (data.code === 1) {
            showToast(data.message || `成功删除 ${count} 条内容`, 'success');
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast(data.message || '批量删除失败', 'error');
        }
    })
    .catch(error => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.remove();
        }
        console.error('批量删除错误:', error);
        showToast('批量删除失败，请重试', 'error');
    });
}

// Toast消息提示函数
function batchUpdateSort() {
    const selectedIds = [];
    document.querySelectorAll('.content-checkbox:checked').forEach(checkbox => {
        selectedIds.push(checkbox.value);
    });

    if (selectedIds.length === 0) {
        showToast('请先选择要排序的内容', 'warning');
        return;
    }

    const startSort = prompt('请输入起始排序号（数字越大排序越靠前）:', '100');
    if (startSort === null) return;

    const startSortNum = parseInt(startSort);
    if (isNaN(startSortNum) || startSortNum < 0) {
        showToast('请输入有效的排序号', 'error');
        return;
    }

    fetch('/content/batchUpdateSort', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            ids: selectedIds.join(','),
            start_sort: startSortNum
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showToast('批量排序更新成功', 'success');
            // 无感刷新
            refreshContentList();
        } else {
            showToast(data.message || '批量排序更新失败', 'error');
        }
    });
}

// 填充创建模态框的分类树
function populateCreateCategories(categories) {
    const dropdown = document.getElementById('createCategoryDropdown');
    dropdown.innerHTML = '';

    // 存储分类数据供后续使用
    window.createCategoriesData = categories;

    function renderCategoryTree(categories, level = 0, container = dropdown) {
        categories.forEach(category => {
            const item = document.createElement('div');
            item.className = `tree-item level-${level + 1}`;
            item.dataset.id = category.id;

            const content = document.createElement('div');
            content.className = 'tree-item-content';
            content.onclick = () => selectCreateCategory(category.id, category.name);

            // 添加展开图标
            if (category.children && category.children.length > 0) {
                const expandIcon = document.createElement('i');
                expandIcon.className = 'fas fa-chevron-right expand-icon';
                expandIcon.onclick = (e) => {
                    e.stopPropagation();
                    toggleCreateTreeNode(expandIcon);
                };
                content.appendChild(expandIcon);
            } else {
                const placeholder = document.createElement('span');
                placeholder.className = 'expand-icon-placeholder';
                content.appendChild(placeholder);
            }

            // 添加分类图标
            const iconDiv = document.createElement('div');
            iconDiv.className = `category-icon level-${level + 1}-icon`;
            const icon = document.createElement('i');
            icon.className = level === 0 ? 'fas fa-circle' : (level === 1 ? 'fas fa-square' : 'fas fa-circle');
            iconDiv.appendChild(icon);
            content.appendChild(iconDiv);

            // 添加分类名称
            const nameSpan = document.createElement('span');
            nameSpan.textContent = category.name;
            content.appendChild(nameSpan);

            item.appendChild(content);

            // 添加子分类
            if (category.children && category.children.length > 0) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'tree-children';
                childrenDiv.style.display = 'none';
                renderCategoryTree(category.children, level + 1, childrenDiv);
                item.appendChild(childrenDiv);
            }

            container.appendChild(item);
        });
    }

    renderCategoryTree(categories);
}

// 填充编辑模态框的分类树
function populateEditCategories(categories) {
    const dropdown = document.getElementById('editCategoryDropdown');
    dropdown.innerHTML = '';

    // 存储分类数据供后续使用
    window.editCategoriesData = categories;

    function renderCategoryTree(categories, level = 0, container = dropdown) {
        categories.forEach(category => {
            const item = document.createElement('div');
            item.className = `tree-item level-${level + 1}`;
            item.dataset.id = category.id;

            const content = document.createElement('div');
            content.className = 'tree-item-content';
            content.onclick = () => selectEditCategory(category.id, category.name);

            // 添加展开图标
            if (category.children && category.children.length > 0) {
                const expandIcon = document.createElement('i');
                expandIcon.className = 'fas fa-chevron-right expand-icon';
                expandIcon.onclick = (e) => {
                    e.stopPropagation();
                    toggleEditTreeNode(expandIcon);
                };
                content.appendChild(expandIcon);
            } else {
                const placeholder = document.createElement('span');
                placeholder.className = 'expand-icon-placeholder';
                content.appendChild(placeholder);
            }

            // 添加分类图标
            const iconDiv = document.createElement('div');
            iconDiv.className = `category-icon level-${level + 1}-icon`;
            const icon = document.createElement('i');
            icon.className = level === 0 ? 'fas fa-circle' : (level === 1 ? 'fas fa-square' : 'fas fa-circle');
            iconDiv.appendChild(icon);
            content.appendChild(iconDiv);

            // 添加分类名称
            const nameSpan = document.createElement('span');
            nameSpan.textContent = category.name;
            content.appendChild(nameSpan);

            item.appendChild(content);

            // 添加子分类
            if (category.children && category.children.length > 0) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'tree-children';
                childrenDiv.style.display = 'none';
                renderCategoryTree(category.children, level + 1, childrenDiv);
                item.appendChild(childrenDiv);
            }

            container.appendChild(item);
        });
    }

    renderCategoryTree(categories);
}

// 获取分类的完整路径
function getCategoryPath(categoryId, categoriesData) {
    function findCategoryPath(categories, targetId, path = []) {
        for (const category of categories) {
            const currentPath = [...path, category.name];

            if (category.id == targetId) {
                return currentPath.join(' > ');
            }

            if (category.children && category.children.length > 0) {
                const result = findCategoryPath(category.children, targetId, currentPath);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }

    return findCategoryPath(categoriesData, categoryId) || '未知分类';
}

function showToast(message, type = 'info') {
    // 简单的toast实现
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10002; min-width: 300px;';
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 切换创建模态框的分类下拉框
function toggleCreateCategoryDropdown() {
    const dropdown = document.getElementById('createCategoryDropdown');
    const input = document.querySelector('#createModal .tree-selector-input');

    if (dropdown.style.display === 'none' || dropdown.style.display === '') {
        dropdown.style.display = 'block';
        input.classList.add('active');
    } else {
        dropdown.style.display = 'none';
        input.classList.remove('active');
    }
}

// 切换编辑模态框的分类下拉框
function toggleEditCategoryDropdown() {
    const dropdown = document.getElementById('editCategoryDropdown');
    const input = document.querySelector('#editModal .tree-selector-input');

    if (dropdown.style.display === 'none' || dropdown.style.display === '') {
        dropdown.style.display = 'block';
        input.classList.add('active');
    } else {
        dropdown.style.display = 'none';
        input.classList.remove('active');
    }
}

// 切换创建模态框的树节点
function toggleCreateTreeNode(expandIcon) {
    const treeItem = expandIcon.closest('.tree-item');
    const children = treeItem.querySelector('.tree-children');

    if (children) {
        const isCurrentlyExpanded = children.style.display === 'block';

        // 手风琴效果：先关闭所有同级的展开项
        const parentContainer = treeItem.parentElement;
        const siblings = parentContainer.querySelectorAll(':scope > .tree-item');

        siblings.forEach(sibling => {
            if (sibling !== treeItem) {
                const siblingChildren = sibling.querySelector('.tree-children');
                const siblingIcon = sibling.querySelector('.expand-icon');

                if (siblingChildren && siblingIcon) {
                    siblingChildren.style.display = 'none';
                    siblingIcon.classList.remove('expanded');
                    siblingIcon.classList.remove('fa-chevron-down');
                    siblingIcon.classList.add('fa-chevron-right');
                }
            }
        });

        // 切换当前项的状态
        if (isCurrentlyExpanded) {
            children.style.display = 'none';
            expandIcon.classList.remove('expanded');
            expandIcon.classList.remove('fa-chevron-down');
            expandIcon.classList.add('fa-chevron-right');
        } else {
            children.style.display = 'block';
            expandIcon.classList.add('expanded');
            expandIcon.classList.remove('fa-chevron-right');
            expandIcon.classList.add('fa-chevron-down');
        }
    }
}

// 切换编辑模态框的树节点
function toggleEditTreeNode(expandIcon) {
    const treeItem = expandIcon.closest('.tree-item');
    const children = treeItem.querySelector('.tree-children');

    if (children) {
        const isCurrentlyExpanded = children.style.display === 'block';

        // 手风琴效果：先关闭所有同级的展开项
        const parentContainer = treeItem.parentElement;
        const siblings = parentContainer.querySelectorAll(':scope > .tree-item');

        siblings.forEach(sibling => {
            if (sibling !== treeItem) {
                const siblingChildren = sibling.querySelector('.tree-children');
                const siblingIcon = sibling.querySelector('.expand-icon');

                if (siblingChildren && siblingIcon) {
                    siblingChildren.style.display = 'none';
                    siblingIcon.classList.remove('expanded');
                    siblingIcon.classList.remove('fa-chevron-down');
                    siblingIcon.classList.add('fa-chevron-right');
                }
            }
        });

        // 切换当前项的状态
        if (isCurrentlyExpanded) {
            children.style.display = 'none';
            expandIcon.classList.remove('expanded');
            expandIcon.classList.remove('fa-chevron-down');
            expandIcon.classList.add('fa-chevron-right');
        } else {
            children.style.display = 'block';
            expandIcon.classList.add('expanded');
            expandIcon.classList.remove('fa-chevron-right');
            expandIcon.classList.add('fa-chevron-down');
        }
    }
}

// 选择创建模态框的分类
function selectCreateCategory(categoryId, categoryName) {
    document.getElementById('create_category_id').value = categoryId;

    // 获取分类的完整路径
    const categoryPath = getCategoryPath(categoryId, window.createCategoriesData);
    document.getElementById('createSelectedCategoryText').textContent = categoryPath;

    document.getElementById('createCategoryDropdown').style.display = 'none';
    document.querySelector('#createModal .tree-selector-input').classList.remove('active');
}

// 选择编辑模态框的分类
function selectEditCategory(categoryId, categoryName) {
    document.getElementById('edit_category_id').value = categoryId;

    // 获取分类的完整路径
    const categoryPath = getCategoryPath(categoryId, window.editCategoriesData);
    document.getElementById('editSelectedCategoryText').textContent = categoryPath;

    document.getElementById('editCategoryDropdown').style.display = 'none';
    document.querySelector('#editModal .tree-selector-input').classList.remove('active');
}

// 提交创建表单
function submitCreate() {
    const form = document.getElementById('createForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 验证表单
    if (!data.category_id || !data.title || !data.content) {
        showToast('请填写所有必填项', 'warning');
        return;
    }

    // 显示加载状态
    setCreateLoading(true);

    // 发送请求
    fetch('/content/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showToast('创建成功', 'success');
            closeCreateModal();
            // 无感刷新
            refreshContentList();
        } else {
            showToast(data.message || '创建失败', 'error');
        }
    })
    .catch(error => {
        showToast('创建失败', 'error');
    })
    .finally(() => {
        setCreateLoading(false);
    });
}

// 提交编辑表单
function submitEdit() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 验证表单
    if (!data.category_id || !data.title || !data.content) {
        showToast('请填写所有必填项', 'warning');
        return;
    }

    // 显示加载状态
    setEditLoading(true);

    // 发送请求
    fetch('/content/edit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showToast('更新成功', 'success');
            closeEditModal();
            // 无感刷新
            refreshContentList();
        } else {
            showToast(data.message || '更新失败', 'error');
        }
    })
    .catch(error => {
        showToast('更新失败', 'error');
    })
    .finally(() => {
        setEditLoading(false);
    });
}

// 设置创建按钮加载状态
function setCreateLoading(loading) {
    const btn = document.getElementById('createSubmitBtn');
    const btnText = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.loading-spinner');

    if (loading) {
        btnText.style.display = 'none';
        spinner.style.display = 'inline-block';
        btn.disabled = true;
    } else {
        btnText.style.display = 'inline-block';
        spinner.style.display = 'none';
        btn.disabled = false;
    }
}

// 设置编辑按钮加载状态
function setEditLoading(loading) {
    const btn = document.getElementById('editSubmitBtn');
    const btnText = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.loading-spinner');

    if (loading) {
        btnText.style.display = 'none';
        spinner.style.display = 'inline-block';
        btn.disabled = true;
    } else {
        btnText.style.display = 'inline-block';
        spinner.style.display = 'none';
        btn.disabled = false;
    }
}

// 切换筛选器分类下拉框
function toggleFilterCategoryDropdown() {
    const dropdown = document.getElementById('filterCategoryDropdown');
    const input = document.querySelector('.filter-tree-selector-input');

    if (dropdown.style.display === 'none' || dropdown.style.display === '') {
        // 加载分类数据
        loadFilterCategories();
        dropdown.style.display = 'block';
        input.classList.add('active');
    } else {
        dropdown.style.display = 'none';
        input.classList.remove('active');
    }
}

// 加载筛选器分类数据
function loadFilterCategories() {
    fetch('/cards/getCategories')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                populateFilterCategories(data.data);
            }
        })
        .catch(error => {
            console.error('获取分类数据失败:', error);
        });
}

// 填充筛选器分类树
function populateFilterCategories(categories) {
    const dropdown = document.getElementById('filterCategoryDropdown');

    // 保留"全部分类"选项，清除其他内容
    const allCategoriesItem = dropdown.querySelector('.filter-tree-item');
    dropdown.innerHTML = '';
    dropdown.appendChild(allCategoriesItem);

    // 存储分类数据供路径计算使用
    window.filterCategoriesData = categories;

    function renderFilterCategoryTree(categories, level = 0, container = dropdown) {
        categories.forEach(category => {
            const item = document.createElement('div');
            item.className = `filter-tree-item level-${level + 1}`;
            item.dataset.id = category.id;

            const content = document.createElement('div');
            content.className = 'filter-tree-item-content';
            content.onclick = () => selectFilterCategory(category.id, getCategoryPath(category.id, window.filterCategoriesData));

            // 添加展开图标
            if (category.children && category.children.length > 0) {
                const expandIcon = document.createElement('i');
                expandIcon.className = 'fas fa-chevron-right expand-icon';
                expandIcon.onclick = (e) => {
                    e.stopPropagation();
                    toggleFilterTreeNode(expandIcon);
                };
                content.appendChild(expandIcon);
            } else {
                const placeholder = document.createElement('span');
                placeholder.className = 'expand-icon-placeholder';
                content.appendChild(placeholder);
            }

            // 添加分类图标
            const iconDiv = document.createElement('div');
            iconDiv.className = `category-icon level-${level + 1}-icon`;
            const icon = document.createElement('i');
            icon.className = level === 0 ? 'fas fa-circle' : (level === 1 ? 'fas fa-square' : 'fas fa-circle');
            iconDiv.appendChild(icon);
            content.appendChild(iconDiv);

            // 添加分类名称
            const nameSpan = document.createElement('span');
            nameSpan.textContent = category.name;
            content.appendChild(nameSpan);

            item.appendChild(content);

            // 添加子分类
            if (category.children && category.children.length > 0) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'tree-children';
                childrenDiv.style.display = 'none';
                renderFilterCategoryTree(category.children, level + 1, childrenDiv);
                item.appendChild(childrenDiv);
            }

            container.appendChild(item);
        });
    }

    renderFilterCategoryTree(categories);
}

// 切换筛选器树节点
function toggleFilterTreeNode(expandIcon) {
    const treeItem = expandIcon.closest('.filter-tree-item');
    const children = treeItem.querySelector('.tree-children');

    if (children) {
        const isCurrentlyExpanded = children.style.display === 'block';

        // 手风琴效果：先关闭所有同级的展开项
        const parentContainer = treeItem.parentElement;
        const siblings = parentContainer.querySelectorAll(':scope > .filter-tree-item');

        siblings.forEach(sibling => {
            if (sibling !== treeItem) {
                const siblingChildren = sibling.querySelector('.tree-children');
                const siblingIcon = sibling.querySelector('.expand-icon');

                if (siblingChildren && siblingIcon) {
                    siblingChildren.style.display = 'none';
                    siblingIcon.classList.remove('expanded');
                    siblingIcon.classList.remove('fa-chevron-down');
                    siblingIcon.classList.add('fa-chevron-right');
                }
            }
        });

        // 切换当前项的状态
        if (isCurrentlyExpanded) {
            children.style.display = 'none';
            expandIcon.classList.remove('expanded');
            expandIcon.classList.remove('fa-chevron-down');
            expandIcon.classList.add('fa-chevron-right');
        } else {
            children.style.display = 'block';
            expandIcon.classList.add('expanded');
            expandIcon.classList.remove('fa-chevron-right');
            expandIcon.classList.add('fa-chevron-down');
        }
    }
}

// 选择筛选器分类
function selectFilterCategory(categoryId, categoryName) {
    document.querySelector('input[name="category_id"]').value = categoryId;
    document.getElementById('filterSelectedCategoryText').textContent = categoryName;
    document.getElementById('filterCategoryDropdown').style.display = 'none';
    document.querySelector('.filter-tree-selector-input').classList.remove('active');
}

// 点击外部关闭下拉框
document.addEventListener('click', function(event) {
    // 处理创建模态框的分类下拉框
    const createSelector = document.querySelector('#createModal .category-tree-selector');
    if (createSelector && !createSelector.contains(event.target)) {
        const createDropdown = document.getElementById('createCategoryDropdown');
        if (createDropdown) {
            createDropdown.style.display = 'none';
            document.querySelector('#createModal .tree-selector-input').classList.remove('active');
        }
    }

    // 处理编辑模态框的分类下拉框
    const editSelector = document.querySelector('#editModal .category-tree-selector');
    if (editSelector && !editSelector.contains(event.target)) {
        const editDropdown = document.getElementById('editCategoryDropdown');
        if (editDropdown) {
            editDropdown.style.display = 'none';
            document.querySelector('#editModal .tree-selector-input').classList.remove('active');
        }
    }

    // 处理筛选器分类下拉框
    const filterSelector = document.querySelector('.filter-category-tree-selector');
    if (filterSelector && !filterSelector.contains(event.target)) {
        const filterDropdown = document.getElementById('filterCategoryDropdown');
        if (filterDropdown) {
            filterDropdown.style.display = 'none';
            document.querySelector('.filter-tree-selector-input').classList.remove('active');
        }
    }
});

// 改变每页显示条数
function changePageSize(limit) {
    const url = new URL(window.location);
    url.searchParams.set('limit', limit);
    url.searchParams.set('page', '1'); // 重置到第一页
    window.location.href = url.toString();
}
</script>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
</body>
</html>
